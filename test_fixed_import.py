#!/usr/bin/env python3
"""
测试修复后的导入功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from src.ui.app_controller import AppController

def test_fixed_import():
    """测试修复后的导入功能"""
    print("🔧 测试修复后的导入功能")
    print("=" * 50)
    
    try:
        # 创建应用控制器
        print("🎮 创建应用控制器...")
        controller = AppController()
        print("✅ 应用控制器创建成功")
        
        # 检查修改后的导入方法
        print("🔍 检查导入方法...")
        if hasattr(controller, 'handle_import_data'):
            print("✅ handle_import_data 方法存在")
        else:
            print("❌ handle_import_data 方法不存在")
            return False
        
        # 检查事件绑定
        if hasattr(controller.main_window, 'on_import_data'):
            if controller.main_window.on_import_data is not None:
                print("✅ 导入事件已绑定")
            else:
                print("❌ 导入事件未绑定")
                return False
        
        print("🚀 启动应用程序...")
        print("💡 现在可以点击'导入数据'按钮测试功能")
        print("📁 建议选择 data/dlt_data.csv 文件")
        
        # 运行应用程序
        controller.run()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_fixed_import()
