#!/usr/bin/env python3
"""
简化版彩票分析系统启动脚本

启动重新设计的简化版彩票数据分析界面。
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from src.ui.simple_main_window import SimpleMainWindow


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('lottery_app.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


def main():
    """主函数"""
    print("启动简化版彩票分析系统...")
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 创建并运行应用
        app = SimpleMainWindow()
        logger.info("应用程序启动成功")
        app.run()
        
    except Exception as e:
        logger.error(f"应用程序运行失败: {e}")
        print(f"错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
