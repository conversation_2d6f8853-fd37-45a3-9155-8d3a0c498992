#!/usr/bin/env python3
"""
彩票类型分析属性详细报告
展示不同彩票类型支持的分析方法和属性
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def generate_analysis_report():
    """生成彩票分析属性报告"""
    print("🎯 彩票类型分析属性详细报告")
    print("=" * 100)
    
    # 1. 彩票类型基本配置
    print("\n📊 1. 彩票类型基本配置")
    print("-" * 60)
    
    lottery_configs = {
        "大乐透 (DLT)": {
            "号码结构": "前区5个号码(1-35) + 后区2个号码(1-12)",
            "大小分界": "前区>18为大号, 后区>7为大号",
            "分区定义": "前区7个区(每区5个号码), 后区2个区",
            "开奖频率": "每周一、三、六",
            "总号码数": 7
        },
        "双色球 (SSQ)": {
            "号码结构": "红球6个号码(1-33) + 蓝球1个号码(1-16)",
            "大小分界": "红球>17为大号, 蓝球>9为大号", 
            "分区定义": "红球7个区, 蓝球2个区",
            "开奖频率": "每周二、四、日",
            "总号码数": 7
        },
        "排列五 (PL5)": {
            "号码结构": "5位数字，每位0-9",
            "大小分界": "≥5为大号",
            "分区定义": "2个区：小号区(0-4)、大号区(5-9)",
            "开奖频率": "每天开奖",
            "总号码数": 5
        }
    }
    
    for lottery_type, config in lottery_configs.items():
        print(f"\n🎲 {lottery_type}:")
        for key, value in config.items():
            print(f"  • {key}: {value}")
    
    # 2. 分析方法对比
    print(f"\n📈 2. 分析方法对比")
    print("-" * 60)
    
    analysis_methods = {
        "奇偶分析": {
            "大乐透": ["前区奇偶分析", "后区奇偶分析", "奇偶模式统计", "奇偶间隔分析"],
            "双色球": ["红球奇偶分析", "蓝球奇偶分析", "奇偶模式统计", "奇偶间隔分析"],
            "排列五": ["整体奇偶分析", "按位奇偶分析", "奇偶模式统计", "奇偶间隔分析"]
        },
        "大小分析": {
            "大乐透": ["前区大小分析", "后区大小分析", "大小模式统计", "大小间隔分析"],
            "双色球": ["红球大小分析", "蓝球大小分析", "大小模式统计", "大小间隔分析"],
            "排列五": ["整体大小分析", "按位大小分析", "大小模式统计", "大小间隔分析"]
        },
        "遗漏分析": {
            "大乐透": ["前区号码遗漏", "后区号码遗漏", "全号码遗漏统计", "遗漏间隔分析"],
            "双色球": ["红球号码遗漏", "蓝球号码遗漏", "全号码遗漏统计", "遗漏间隔分析"],
            "排列五": ["整体号码遗漏", "按位号码遗漏", "全号码遗漏统计", "遗漏间隔分析"]
        },
        "分区比分析": {
            "大乐透": ["前区7区分布", "后区2区分布", "分区比模式", "分区间隔分析"],
            "双色球": ["红球7区分布", "蓝球2区分布", "分区比模式", "分区间隔分析"],
            "排列五": ["2区分布", "按位分区分析", "分区比模式", "分区间隔分析"]
        }
    }
    
    for method, details in analysis_methods.items():
        print(f"\n🔍 {method}:")
        for lottery_type, features in details.items():
            print(f"  📊 {lottery_type}:")
            for feature in features:
                print(f"    • {feature}")
    
    # 3. 分析结果属性
    print(f"\n📋 3. 分析结果属性结构")
    print("-" * 60)
    
    result_attributes = {
        "基础属性": [
            "zone_type: 区域类型 ('front'/'back')",
            "zone_name: 区域名称 (前区/后区/红球区/蓝球区/整体)",
            "total_periods: 总期数",
            "date_range: 数据时间范围"
        ],
        "模式统计": [
            "pattern_statistics: 模式统计信息",
            "most_common_patterns: 最常见模式(前10个)",
            "latest_pattern: 最新模式",
            "latest_missing_periods: 最新模式遗漏期数"
        ],
        "数量统计": [
            "odd_count_stats: 奇数数量统计",
            "even_count_stats: 偶数数量统计",
            "big_count_stats: 大号数量统计",
            "small_count_stats: 小号数量统计"
        ],
        "详细数据": [
            "recent_details: 最近20期详细数据",
            "all_details: 全部详细数据",
            "occurrences: 出现详情",
            "intervals: 间隔数据"
        ],
        "特殊属性": [
            "position_statistics: 按位统计(排列五专有)",
            "zone_definitions: 分区定义",
            "zone_totals: 分区总计",
            "zone_averages: 分区平均值"
        ]
    }
    
    for category, attributes in result_attributes.items():
        print(f"\n📊 {category}:")
        for attr in attributes:
            print(f"  • {attr}")
    
    # 4. 特殊功能对比
    print(f"\n🎯 4. 特殊功能对比")
    print("-" * 60)
    
    special_features = {
        "大乐透": [
            "✅ 前区后区分别分析",
            "✅ 7区分布分析",
            "✅ 35个前区号码遗漏",
            "✅ 12个后区号码遗漏",
            "✅ 支持所有基础分析"
        ],
        "双色球": [
            "✅ 红球蓝球分别分析", 
            "✅ 7区分布分析",
            "✅ 33个红球号码遗漏",
            "✅ 16个蓝球号码遗漏",
            "✅ 支持所有基础分析"
        ],
        "排列五": [
            "✅ 按位分析功能",
            "✅ 5位数字独立分析",
            "✅ 10个数字(0-9)遗漏",
            "✅ 位置特定遗漏分析",
            "✅ 简化的2区分布"
        ]
    }
    
    for lottery_type, features in special_features.items():
        print(f"\n🎲 {lottery_type}:")
        for feature in features:
            print(f"  {feature}")
    
    # 5. 使用示例
    print(f"\n💡 5. 使用示例")
    print("-" * 60)
    
    examples = {
        "大乐透分析": [
            "analyzer = DLTAnalyzer()",
            "analyzer.load_data(data_list)",
            "result = analyzer.analyze_odd_even_distribution('front')",
            "missing = analyzer.calculate_all_numbers_missing('back')"
        ],
        "双色球分析": [
            "analyzer = SSQAnalyzer()",
            "analyzer.load_data(data_list)", 
            "result = analyzer.analyze_big_small_distribution('front')",
            "zone_result = analyzer.analyze_zone_ratio_distribution('front')"
        ],
        "排列五分析": [
            "analyzer = PL5Analyzer()",
            "analyzer.load_data(data_list)",
            "result = analyzer.analyze_odd_even_distribution('front')",
            "pos_result = analyzer.calculate_position_number_intervals(5, 1)"
        ]
    }
    
    for example_type, code_lines in examples.items():
        print(f"\n🔧 {example_type}:")
        for line in code_lines:
            print(f"  {line}")

def main():
    """主函数"""
    generate_analysis_report()
    
    print(f"\n🎉 报告生成完成！")
    print(f"📝 总结:")
    print(f"  • 支持3种彩票类型：大乐透、双色球、排列五")
    print(f"  • 提供4类分析方法：奇偶、大小、遗漏、分区比")
    print(f"  • 每种分析都有详细的统计属性和结果数据")
    print(f"  • 排列五支持独特的按位分析功能")
    print(f"  • 所有分析器都继承自统一的基类LotteryAnalyzer")

if __name__ == "__main__":
    main()
