#!/usr/bin/env python3
"""
测试分析功能是否正常工作
"""

import sys
import os
import tempfile
import csv
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from src.ui.app_controller import AppController
from src.data.config import LOTTERY_TYPE_DLT


def create_test_data_file():
    """创建测试数据文件"""
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
    
    # 大乐透测试数据
    data = [
        ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"],
        ["24001", "2024-01-01", "1", "5", "15", "25", "35", "2", "8"],
        ["24002", "2024-01-03", "3", "7", "17", "27", "33", "1", "9"],
        ["24003", "2024-01-06", "2", "8", "18", "28", "34", "3", "10"],
        ["24004", "2024-01-08", "4", "9", "19", "29", "31", "5", "11"],
        ["24005", "2024-01-10", "6", "11", "21", "30", "32", "4", "12"]
    ]
    
    writer = csv.writer(temp_file)
    for row in data:
        writer.writerow(row)
    
    temp_file.close()
    return temp_file.name


def test_analysis_function():
    """测试分析功能"""
    print("测试分析功能...")
    
    try:
        # 创建应用程序控制器
        app_controller = AppController()
        
        # 检查事件绑定
        print(f"导入数据回调: {app_controller.main_window.on_import_data}")
        print(f"开始分析回调: {app_controller.main_window.on_start_analysis}")
        print(f"导出结果回调: {app_controller.main_window.on_export_results}")
        print(f"清除结果回调: {app_controller.main_window.on_clear_results}")
        
        # 创建测试数据文件
        test_file = create_test_data_file()
        print(f"创建测试数据文件: {test_file}")
        
        # 模拟数据导入
        print("模拟数据导入...")
        data_list = app_controller.data_reader.read_csv(test_file, LOTTERY_TYPE_DLT)
        validation_result = app_controller.data_validator.validate_data_list(data_list)
        
        # 过滤有效数据
        valid_data = []
        for data in data_list:
            is_valid, _ = app_controller.data_validator.validate_lottery_data(data)
            if is_valid:
                valid_data.append(data)
        
        app_controller.current_data = valid_data
        print(f"加载了 {len(valid_data)} 条有效数据")
        
        # 设置分析选项
        app_controller.main_window.analysis_options['odd_even'].set(True)
        app_controller.main_window.analysis_options['big_small'].set(True)
        app_controller.main_window.analysis_options['missing'].set(False)
        app_controller.main_window.analysis_options['zone_ratio'].set(False)
        
        # 测试分析功能
        print("测试分析功能...")
        
        # 直接调用分析方法
        options = app_controller.main_window.get_analysis_options()
        print(f"分析选项: {options}")
        
        # 创建分析器
        analyzer = app_controller.create_analyzer(LOTTERY_TYPE_DLT)
        analyzer.load_data(valid_data)
        
        # 执行分析
        results = app_controller.perform_analysis(options)
        print("分析结果:")
        print(results[:500] + "..." if len(results) > 500 else results)
        
        # 清理临时文件
        os.unlink(test_file)
        
        print("✅ 分析功能测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 分析功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_ui_integration():
    """测试UI集成"""
    print("\n测试UI集成...")
    
    try:
        # 这里我们不能直接测试GUI，但可以验证回调函数
        from src.ui.app_controller import AppController
        
        app_controller = AppController()
        
        # 验证回调函数是否正确绑定
        assert app_controller.main_window.on_start_analysis is not None
        assert callable(app_controller.main_window.on_start_analysis)
        
        print("✅ UI集成测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("彩票号码分析系统 - 分析功能测试")
    print("=" * 60)
    
    # 测试分析功能
    analysis_ok = test_analysis_function()
    
    # 测试UI集成
    ui_ok = test_ui_integration()
    
    print("\n" + "=" * 60)
    if analysis_ok and ui_ok:
        print("🎉 所有测试通过！分析功能正常工作。")
        print("\n要启动完整的GUI应用程序，请运行:")
        print("python main.py")
    else:
        print("❌ 部分测试失败，需要修复问题。")
    print("=" * 60)


if __name__ == "__main__":
    main()
