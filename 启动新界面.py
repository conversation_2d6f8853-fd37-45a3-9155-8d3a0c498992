#!/usr/bin/env python3
"""
启动修正后的彩票分析系统

确保使用正确的列顺序和完整的功能。
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def main():
    """主函数"""
    print("🚀 启动修正版彩票分析系统...")
    print("📋 表格列顺序：")
    print("1. 开奖日期")
    print("2. 期号")
    print("3. 开奖号码")
    print("4. 红球")
    print("5. 篮球")
    print("6. 奇偶比")
    print("7. 奇偶排布")
    print("8. 上次奇偶排布")
    print("9. 大小比")
    print("10. 大小排布")
    print("11. 上次大小排布")
    print()
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        from src.ui.simple_main_window import SimpleMainWindow
        
        # 创建并运行应用
        app = SimpleMainWindow()
        print("✅ 应用程序启动成功")
        app.run()
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
