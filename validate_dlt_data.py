#!/usr/bin/env python3
"""
大乐透数据验证脚本
对dlt_data.csv文件进行详细的数据验证分析
"""

import sys
import os
import pandas as pd
from datetime import datetime
import re

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from src.data.reader import DataReader
from src.data.validator import DataValidator
from src.data.config import LOTTERY_TYPE_DLT

def validate_dlt_csv_file(file_path):
    """验证大乐透CSV文件"""
    print(f"🔍 开始验证文件: {file_path}")
    print("=" * 80)
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        # 读取CSV文件
        print("📖 读取CSV文件...")
        df = pd.read_csv(file_path, encoding='utf-8')
        print(f"✅ 成功读取文件，共 {len(df)} 行数据")
        
        # 1. 检查列名格式
        print("\n1️⃣ 检查列名格式...")
        expected_columns = ['期号', '开奖日期', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        actual_columns = list(df.columns)
        
        print(f"期望列名: {expected_columns}")
        print(f"实际列名: {actual_columns}")
        
        missing_columns = set(expected_columns) - set(actual_columns)
        extra_columns = set(actual_columns) - set(expected_columns)
        
        if missing_columns:
            print(f"❌ 缺少列: {missing_columns}")
        if extra_columns:
            print(f"⚠️  额外列: {extra_columns}")
        if not missing_columns and not extra_columns:
            print("✅ 列名格式正确")
        
        # 2. 逐行验证数据
        print("\n2️⃣ 逐行验证数据...")
        
        valid_count = 0
        invalid_count = 0
        error_details = []
        
        for index, row in df.iterrows():
            row_errors = []
            
            try:
                # 验证期号
                issue_number = str(row['期号']).strip()
                if not re.match(r'^\d{4,6}$', issue_number):
                    row_errors.append(f"期号格式错误: {issue_number}")
                
                # 验证日期
                date_str = str(row['开奖日期']).strip()
                try:
                    datetime.strptime(date_str, '%Y-%m-%d')
                except ValueError:
                    row_errors.append(f"日期格式错误: {date_str}")
                
                # 验证号码
                try:
                    numbers = [
                        int(row['红球1']), int(row['红球2']), int(row['红球3']), 
                        int(row['红球4']), int(row['红球5']),
                        int(row['蓝球1']), int(row['蓝球2'])
                    ]
                    
                    # 检查前区号码范围 (1-35)
                    front_numbers = numbers[:5]
                    for i, num in enumerate(front_numbers):
                        if not (1 <= num <= 35):
                            row_errors.append(f"前区号码{i+1}超出范围(1-35): {num}")
                    
                    # 检查前区号码重复
                    if len(set(front_numbers)) != len(front_numbers):
                        row_errors.append(f"前区号码重复: {front_numbers}")
                    
                    # 检查后区号码范围 (1-12)
                    back_numbers = numbers[5:7]
                    for i, num in enumerate(back_numbers):
                        if not (1 <= num <= 12):
                            row_errors.append(f"后区号码{i+1}超出范围(1-12): {num}")
                    
                    # 检查后区号码重复
                    if len(set(back_numbers)) != len(back_numbers):
                        row_errors.append(f"后区号码重复: {back_numbers}")
                        
                except (ValueError, TypeError) as e:
                    row_errors.append(f"号码格式错误: {e}")
                
            except Exception as e:
                row_errors.append(f"数据解析错误: {e}")
            
            if row_errors:
                invalid_count += 1
                error_details.append({
                    'row': index + 2,  # +2 因为pandas从0开始，且有标题行
                    'issue': issue_number if 'issue_number' in locals() else 'N/A',
                    'errors': row_errors
                })
            else:
                valid_count += 1
        
        # 3. 输出验证结果
        print(f"\n3️⃣ 验证结果统计:")
        print(f"总数据行数: {len(df)}")
        print(f"有效数据: {valid_count} 行")
        print(f"无效数据: {invalid_count} 行")
        print(f"验证通过率: {valid_count/len(df)*100:.2f}%")
        
        # 4. 显示错误详情
        if error_details:
            print(f"\n4️⃣ 错误详情 (显示前20个错误):")
            for i, error in enumerate(error_details[:20]):
                print(f"第{error['row']}行 (期号: {error['issue']}):")
                for err in error['errors']:
                    print(f"  ❌ {err}")
                print()
            
            if len(error_details) > 20:
                print(f"... 还有 {len(error_details) - 20} 个错误未显示")
        else:
            print("\n🎉 所有数据验证通过！")
        
        # 5. 使用系统验证器进行验证
        print(f"\n5️⃣ 使用系统验证器验证...")
        try:
            reader = DataReader()
            validator = DataValidator()
            
            # 读取数据
            data_list = reader.read_csv(file_path, LOTTERY_TYPE_DLT)
            print(f"✅ 系统读取器成功解析 {len(data_list)} 条数据")
            
            # 验证数据
            validation_result = validator.validate_data_list(data_list)
            print(f"✅ 系统验证器结果:")
            print(f"  总数: {validation_result['total_count']}")
            print(f"  有效: {validation_result['valid_count']}")
            print(f"  无效: {validation_result['invalid_count']}")
            
            if validation_result['errors']:
                print(f"  错误详情 (前10个):")
                for error in validation_result['errors'][:10]:
                    print(f"    ❌ {error}")
            
            if validation_result['warnings']:
                print(f"  警告信息:")
                for warning in validation_result['warnings']:
                    print(f"    ⚠️  {warning}")
                    
        except Exception as e:
            print(f"❌ 系统验证器执行失败: {e}")
    
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")

if __name__ == "__main__":
    # 验证dlt_data.csv文件
    file_path = "data/dlt_data.csv"
    validate_dlt_csv_file(file_path)
