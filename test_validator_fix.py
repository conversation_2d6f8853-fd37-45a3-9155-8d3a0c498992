#!/usr/bin/env python3
"""
测试修改后的验证器
验证期号格式修改是否生效
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from src.data.validator import DataValidator
from src.data.reader import DataReader
from src.data.config import LOTTERY_TYPE_DLT

def test_issue_number_validation():
    """测试期号验证功能"""
    print("🧪 测试修改后的期号验证功能")
    print("=" * 50)
    
    validator = DataValidator()
    
    # 测试不同格式的期号
    test_cases = [
        # 4位格式 (2007-2009年)
        ("7001", True, "4位格式 - 2007年第1期"),
        ("7152", True, "4位格式 - 2007年最后一期"),
        ("8001", True, "4位格式 - 2008年第1期"),
        ("9153", True, "4位格式 - 2009年最后一期"),
        
        # 5位格式 (2010年以后)
        ("10001", True, "5位格式 - 2010年第1期"),
        ("24001", True, "5位格式 - 2024年第1期"),
        ("25084", True, "5位格式 - 2025年第84期"),
        
        # 6位格式 (预留)
        ("240001", True, "6位格式 - 预留格式"),
        
        # 无效格式
        ("123", False, "3位格式 - 太短"),
        ("1234567", False, "7位格式 - 太长"),
        ("abc123", False, "包含字母"),
        ("12-34", False, "包含特殊字符"),
        ("", False, "空字符串"),
        ("  ", False, "空白字符"),
    ]
    
    print("📋 期号格式测试:")
    passed = 0
    failed = 0
    
    for issue_number, expected, description in test_cases:
        result = validator.validate_issue_number(issue_number)
        status = "✅" if result == expected else "❌"
        
        print(f"  {status} {issue_number:>8} -> {result:>5} ({description})")
        
        if result == expected:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 测试结果:")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"成功率: {passed/(passed+failed)*100:.1f}%")
    
    return failed == 0

def test_dlt_data_validation():
    """测试大乐透数据验证"""
    print(f"\n🎯 测试大乐透数据文件验证")
    print("=" * 50)
    
    file_path = "data/dlt_data.csv"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 使用修改后的验证器
        reader = DataReader()
        validator = DataValidator()
        
        print("📖 读取数据文件...")
        data_list = reader.read_csv(file_path, LOTTERY_TYPE_DLT)
        print(f"✅ 成功读取 {len(data_list)} 条数据")
        
        print("🔍 验证数据...")
        validation_result = validator.validate_data_list(data_list)
        
        print(f"\n📊 验证结果:")
        print(f"总数据: {validation_result['total_count']}")
        print(f"有效数据: {validation_result['valid_count']}")
        print(f"无效数据: {validation_result['invalid_count']}")
        print(f"验证通过率: {validation_result['valid_count']/validation_result['total_count']*100:.2f}%")
        
        # 显示错误信息
        if validation_result['errors']:
            print(f"\n❌ 错误详情 (前10个):")
            for error in validation_result['errors'][:10]:
                print(f"  {error}")
        else:
            print(f"\n🎉 所有数据验证通过！")
        
        # 显示警告信息
        if validation_result['warnings']:
            print(f"\n⚠️  警告信息:")
            for warning in validation_result['warnings']:
                print(f"  {warning}")
        
        return validation_result['invalid_count'] == 0
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def test_specific_issue_numbers():
    """测试具体的期号样本"""
    print(f"\n🔬 测试具体期号样本")
    print("=" * 50)
    
    validator = DataValidator()
    
    # 从实际数据中取样本
    sample_issues = [
        "7001",   # 2007年第1期
        "7152",   # 2007年最后一期  
        "8001",   # 2008年第1期
        "9153",   # 2009年最后一期
        "10001",  # 2010年第1期
        "24001",  # 2024年第1期
        "25084",  # 2025年第84期
    ]
    
    print("📋 实际数据期号测试:")
    all_passed = True
    
    for issue in sample_issues:
        result = validator.validate_issue_number(issue)
        status = "✅" if result else "❌"
        print(f"  {status} 期号 {issue}: {result}")
        
        if not result:
            all_passed = False
    
    return all_passed

def main():
    """主测试函数"""
    print("🚀 开始测试修改后的验证器")
    print("=" * 80)
    
    # 测试1: 期号格式验证
    test1_passed = test_issue_number_validation()
    
    # 测试2: 具体期号样本
    test2_passed = test_specific_issue_numbers()
    
    # 测试3: 完整数据文件验证
    test3_passed = test_dlt_data_validation()
    
    # 总结
    print(f"\n🏁 测试总结")
    print("=" * 80)
    print(f"期号格式测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"期号样本测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"数据文件测试: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed and test3_passed:
        print(f"\n🎉 所有测试通过！验证器修改成功！")
        print(f"✅ 现在支持4-6位期号格式")
        print(f"✅ 历史数据(2007-2009年)可以正常验证")
        print(f"✅ 现代数据(2010年以后)继续正常工作")
    else:
        print(f"\n❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
