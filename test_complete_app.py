#!/usr/bin/env python3
"""
测试完整应用程序功能
"""

import sys
import os
import tempfile
import csv
import threading
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def create_test_data_file():
    """创建测试数据文件"""
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
    
    # 大乐透测试数据
    data = [
        ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"],
        ["24001", "2024-01-01", "1", "5", "15", "25", "35", "2", "8"],
        ["24002", "2024-01-03", "3", "7", "17", "27", "33", "1", "9"],
        ["24003", "2024-01-06", "2", "8", "18", "28", "34", "3", "10"],
        ["24004", "2024-01-08", "4", "9", "19", "29", "31", "5", "11"],
        ["24005", "2024-01-10", "6", "11", "21", "30", "32", "4", "12"],
        ["24006", "2024-01-13", "7", "12", "22", "26", "35", "6", "7"],
        ["24007", "2024-01-15", "1", "10", "20", "24", "33", "8", "9"],
        ["24008", "2024-01-17", "5", "13", "23", "27", "34", "2", "11"],
        ["24009", "2024-01-20", "3", "14", "16", "28", "32", "1", "12"],
        ["24010", "2024-01-22", "8", "15", "25", "29", "31", "4", "6"]
    ]
    
    writer = csv.writer(temp_file)
    for row in data:
        writer.writerow(row)
    
    temp_file.close()
    return temp_file.name


def test_backend_functionality():
    """测试后端功能"""
    print("=" * 60)
    print("测试后端分析功能")
    print("=" * 60)
    
    try:
        from src.data.reader import DataReader
        from src.data.validator import DataValidator
        from src.data.config import LOTTERY_TYPE_DLT
        from src.analysis.dlt_analyzer import DLTAnalyzer
        
        # 创建测试数据
        test_file = create_test_data_file()
        print(f"✅ 创建测试数据文件: {test_file}")
        
        # 测试数据读取
        data_reader = DataReader()
        data_list = data_reader.read_csv(test_file, LOTTERY_TYPE_DLT)
        print(f"✅ 成功读取 {len(data_list)} 条数据")
        
        # 测试数据验证
        data_validator = DataValidator()
        validation_result = data_validator.validate_data_list(data_list)
        print(f"✅ 数据验证完成: {validation_result['valid_count']}/{validation_result['total_count']} 条有效")
        
        # 测试分析功能
        analyzer = DLTAnalyzer()
        analyzer.load_data(data_list)
        print("✅ 分析器加载数据成功")
        
        # 执行各种分析
        print("\n执行分析:")
        
        # 奇偶分析
        odd_even_result = analyzer.analyze_odd_even_distribution('front')
        print(f"  奇偶分析: {odd_even_result['total_periods']} 期数据，最新模式: {odd_even_result['latest_pattern']}")
        
        # 大小分析
        big_small_result = analyzer.analyze_big_small_distribution('front')
        print(f"  大小分析: {big_small_result['total_periods']} 期数据，最新模式: {big_small_result['latest_pattern']}")
        
        # 遗漏分析
        missing_result = analyzer.calculate_all_numbers_missing('front')
        print(f"  遗漏分析: {missing_result['total_numbers']} 个号码，{missing_result['total_periods']} 期数据")
        
        # 分区比分析
        zone_result = analyzer.analyze_zone_ratio('front')
        print(f"  分区比分析: {zone_result['total_periods']} 期数据，最新模式: {zone_result['latest_pattern']}")
        
        # 清理
        os.unlink(test_file)
        
        print("\n✅ 后端功能测试全部通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 后端功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_app_controller():
    """测试应用程序控制器"""
    print("\n" + "=" * 60)
    print("测试应用程序控制器")
    print("=" * 60)
    
    try:
        from src.ui.app_controller import AppController
        from src.data.config import LOTTERY_TYPE_DLT
        
        # 创建控制器
        app_controller = AppController()
        print("✅ 应用程序控制器创建成功")
        
        # 检查事件绑定
        assert app_controller.main_window.on_start_analysis is not None
        assert callable(app_controller.main_window.on_start_analysis)
        print("✅ 事件绑定检查通过")
        
        # 模拟数据导入
        test_file = create_test_data_file()
        data_list = app_controller.data_reader.read_csv(test_file, LOTTERY_TYPE_DLT)
        
        # 验证数据
        validation_result = app_controller.data_validator.validate_data_list(data_list)
        valid_data = []
        for data in data_list:
            is_valid, _ = app_controller.data_validator.validate_lottery_data(data)
            if is_valid:
                valid_data.append(data)
        
        app_controller.current_data = valid_data
        print(f"✅ 模拟数据导入成功: {len(valid_data)} 条有效数据")
        
        # 设置分析选项
        options = {
            'odd_even': True,
            'big_small': True,
            'missing': True,
            'zone_ratio': True
        }
        
        # 执行分析
        analyzer = app_controller.create_analyzer(LOTTERY_TYPE_DLT)
        analyzer.load_data(valid_data)
        results = app_controller.perform_analysis(options)
        
        print("✅ 分析执行成功")
        print(f"  结果长度: {len(results)} 字符")
        print(f"  结果预览: {results[:200]}...")
        
        # 清理
        os.unlink(test_file)
        
        print("\n✅ 应用程序控制器测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 应用程序控制器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_startup():
    """测试GUI启动（非阻塞）"""
    print("\n" + "=" * 60)
    print("测试GUI启动")
    print("=" * 60)
    
    try:
        from src.ui.app_controller import AppController
        
        def run_gui():
            """在单独线程中运行GUI"""
            try:
                app = AppController()
                # 不调用run()方法，只是创建界面
                print("✅ GUI界面创建成功")
                return True
            except Exception as e:
                print(f"❌ GUI创建失败: {str(e)}")
                return False
        
        # 在单独线程中测试GUI创建
        gui_thread = threading.Thread(target=run_gui)
        gui_thread.daemon = True
        gui_thread.start()
        gui_thread.join(timeout=5)  # 等待5秒
        
        if gui_thread.is_alive():
            print("⚠️ GUI创建超时，但这可能是正常的")
        else:
            print("✅ GUI启动测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI启动测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("彩票号码分析系统 - 完整功能测试")
    print("=" * 80)
    
    # 测试后端功能
    backend_ok = test_backend_functionality()
    
    # 测试应用程序控制器
    controller_ok = test_app_controller()
    
    # 测试GUI启动
    gui_ok = test_gui_startup()
    
    # 总结
    print("\n" + "=" * 80)
    print("测试结果总结:")
    print(f"  后端功能: {'✅ 通过' if backend_ok else '❌ 失败'}")
    print(f"  应用控制器: {'✅ 通过' if controller_ok else '❌ 失败'}")
    print(f"  GUI启动: {'✅ 通过' if gui_ok else '❌ 失败'}")
    
    if backend_ok and controller_ok:
        print("\n🎉 核心功能测试全部通过！")
        print("\n分析功能已经正确实现，包括:")
        print("  ✅ 数据导入和验证")
        print("  ✅ 奇偶分析")
        print("  ✅ 大小分析") 
        print("  ✅ 号码遗漏分析")
        print("  ✅ 分区比分析")
        print("  ✅ 结果格式化输出")
        
        print("\n要启动完整的GUI应用程序，请运行:")
        print("  python main.py")
        
        print("\n使用步骤:")
        print("  1. 启动程序")
        print("  2. 选择彩票类型（大乐透）")
        print("  3. 点击'导入数据'选择CSV文件")
        print("  4. 选择分析选项")
        print("  5. 点击'开始分析'")
        print("  6. 查看分析结果")
        
    else:
        print("\n❌ 部分功能存在问题，需要修复。")
    
    print("=" * 80)


if __name__ == "__main__":
    main()
