#!/usr/bin/env python3
"""
测试自动分析功能
"""

import sys
import os
import tempfile
import csv
import threading
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from src.ui.app_controller import AppController
from src.data.config import LOTTERY_TYPE_DLT


def create_test_data_file():
    """创建测试数据文件"""
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
    
    # 大乐透测试数据
    data = [
        ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"],
        ["24001", "2024-01-01", "1", "5", "15", "25", "35", "2", "8"],
        ["24002", "2024-01-03", "3", "7", "17", "27", "33", "1", "9"],
        ["24003", "2024-01-06", "2", "8", "18", "28", "34", "3", "10"],
        ["24004", "2024-01-08", "4", "9", "19", "29", "31", "5", "11"],
        ["24005", "2024-01-10", "6", "11", "21", "30", "32", "4", "12"],
        ["24006", "2024-01-13", "7", "12", "22", "26", "35", "6", "7"],
        ["24007", "2024-01-15", "1", "10", "20", "24", "33", "8", "9"],
        ["24008", "2024-01-17", "5", "13", "23", "27", "34", "2", "11"],
        ["24009", "2024-01-20", "3", "14", "16", "28", "32", "1", "12"],
        ["24010", "2024-01-22", "8", "15", "25", "29", "31", "4", "6"],
        ["24011", "2024-01-24", "9", "16", "26", "30", "32", "5", "7"],
        ["24012", "2024-01-27", "10", "17", "27", "31", "33", "6", "8"],
        ["24013", "2024-01-29", "11", "18", "28", "32", "34", "7", "9"],
        ["24014", "2024-01-31", "12", "19", "29", "33", "35", "8", "10"],
        ["24015", "2024-02-03", "13", "20", "30", "34", "1", "9", "11"]
    ]
    
    writer = csv.writer(temp_file)
    for row in data:
        writer.writerow(row)
    
    temp_file.close()
    return temp_file.name


def test_auto_analysis():
    """测试自动分析功能"""
    print("=" * 80)
    print("测试自动分析功能")
    print("=" * 80)
    
    try:
        # 创建应用程序控制器
        app_controller = AppController()
        print("✅ 应用程序控制器创建成功")
        
        # 创建测试数据文件
        test_file = create_test_data_file()
        print(f"✅ 创建测试数据文件: {test_file}")
        
        # 模拟数据导入过程
        print("\n开始模拟数据导入...")
        
        # 读取数据
        data_list = app_controller.data_reader.read_csv(test_file, LOTTERY_TYPE_DLT)
        print(f"✅ 成功读取 {len(data_list)} 条数据")
        
        # 验证数据
        validation_result = app_controller.data_validator.validate_data_list(data_list)
        print(f"✅ 数据验证: {validation_result['valid_count']}/{validation_result['total_count']} 条有效")
        
        # 过滤有效数据
        valid_data = []
        for data in data_list:
            is_valid, _ = app_controller.data_validator.validate_lottery_data(data)
            if is_valid:
                valid_data.append(data)
        
        app_controller.current_data = valid_data
        print(f"✅ 设置当前数据: {len(valid_data)} 条有效数据")
        
        # 测试自动分析功能
        print("\n开始测试自动分析...")
        
        # 创建分析器
        analyzer = app_controller.create_analyzer(LOTTERY_TYPE_DLT)
        analyzer.load_data(valid_data)
        print("✅ 分析器创建和数据加载成功")
        
        # 执行所有分析
        all_options = {
            'odd_even': True,
            'big_small': True,
            'missing': True,
            'zone_ratio': True
        }
        
        print("执行所有分析项目...")
        results = app_controller.perform_analysis(all_options)
        
        print("✅ 自动分析执行成功")
        print(f"结果长度: {len(results)} 字符")
        
        # 显示部分结果
        print("\n" + "=" * 80)
        print("分析结果预览:")
        print("=" * 80)
        lines = results.split('\n')
        for i, line in enumerate(lines[:30]):  # 显示前30行
            print(line)
        if len(lines) > 30:
            print("...")
            print(f"（还有 {len(lines) - 30} 行结果）")
        
        # 清理
        os.unlink(test_file)
        
        print("\n" + "=" * 80)
        print("🎉 自动分析功能测试成功！")
        print("=" * 80)
        
        print("\n📋 功能确认:")
        print("  ✅ 数据导入后自动执行所有分析")
        print("  ✅ 奇偶分析（前区/后区）")
        print("  ✅ 大小分析（前区/后区）")
        print("  ✅ 号码遗漏分析（前区/后区）")
        print("  ✅ 分区比分析（前区/后区）")
        print("  ✅ 完整结果格式化输出")
        
        print("\n🚀 新的使用流程:")
        print("  1. 运行: python main.py")
        print("  2. 选择彩票类型（大乐透）")
        print("  3. 点击'导入数据'选择CSV文件")
        print("  4. 系统自动执行所有分析并显示结果")
        print("  5. 可选择'手动分析'进行特定项目分析")
        print("  6. 查看和导出结果")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_manual_analysis():
    """测试手动分析功能（保留原功能）"""
    print("\n" + "=" * 80)
    print("测试手动分析功能")
    print("=" * 80)
    
    try:
        app_controller = AppController()
        
        # 创建测试数据
        test_file = create_test_data_file()
        data_list = app_controller.data_reader.read_csv(test_file, LOTTERY_TYPE_DLT)
        
        valid_data = []
        for data in data_list:
            is_valid, _ = app_controller.data_validator.validate_lottery_data(data)
            if is_valid:
                valid_data.append(data)
        
        app_controller.current_data = valid_data
        app_controller.current_analyzer = app_controller.create_analyzer(LOTTERY_TYPE_DLT)
        app_controller.current_analyzer.load_data(valid_data)
        
        # 测试特定分析选项
        print("测试特定分析选项...")
        
        # 只执行奇偶分析
        odd_even_options = {
            'odd_even': True,
            'big_small': False,
            'missing': False,
            'zone_ratio': False
        }
        
        results = app_controller.perform_analysis(odd_even_options)
        print("✅ 单项分析（奇偶）执行成功")
        
        # 只执行遗漏分析
        missing_options = {
            'odd_even': False,
            'big_small': False,
            'missing': True,
            'zone_ratio': False
        }
        
        results = app_controller.perform_analysis(missing_options)
        print("✅ 单项分析（遗漏）执行成功")
        
        # 清理
        os.unlink(test_file)
        
        print("✅ 手动分析功能测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 手动分析测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("彩票号码分析系统 - 自动分析功能测试")
    print("=" * 80)
    
    # 测试自动分析
    auto_ok = test_auto_analysis()
    
    # 测试手动分析
    manual_ok = test_manual_analysis()
    
    # 总结
    print("\n" + "=" * 80)
    print("测试结果总结:")
    print(f"  自动分析功能: {'✅ 通过' if auto_ok else '❌ 失败'}")
    print(f"  手动分析功能: {'✅ 通过' if manual_ok else '❌ 失败'}")
    
    if auto_ok and manual_ok:
        print("\n🎉 所有功能测试通过！")
        print("\n✨ 新功能特点:")
        print("  - 导入数据后自动执行所有分析")
        print("  - 无需手动点击分析按钮")
        print("  - 保留手动分析功能供特定需求使用")
        print("  - 完整的分析结果立即显示")
        
        print("\n📝 用户体验改进:")
        print("  - 简化操作流程：导入即分析")
        print("  - 提高效率：一步完成所有分析")
        print("  - 保持灵活性：支持手动选择分析项目")
        
    else:
        print("\n❌ 部分功能存在问题，需要修复。")
    
    print("=" * 80)


if __name__ == "__main__":
    main()
