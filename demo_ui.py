#!/usr/bin/env python3
"""
UI界面演示脚本
用于验证主窗口界面的功能
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_main_window():
    """测试主窗口"""
    try:
        from src.ui.main_window import MainWindow
        
        print("创建主窗口...")
        app = MainWindow()
        
        # 设置一些测试数据
        app.data_file_path.set("data/dlt_data.csv")
        app.set_results_text("这是一个测试结果\n界面功能正常工作！")
        
        print("主窗口创建成功！")
        print("界面包含以下组件：")
        print("- 菜单栏（文件、帮助）")
        print("- 工具栏（导入数据、开始分析、导出结果）")
        print("- 彩票类型选择区（单选按钮）")
        print("- 分析选项区（复选框）")
        print("- 操作按钮区（开始分析、清除结果）")
        print("- 结果显示区（文本框）")
        print("\n窗口已打开，请在GUI中验证界面布局和功能...")
        
        # 运行主窗口
        app.run()
        
    except Exception as e:
        print(f"测试失败：{str(e)}")
        import traceback
        traceback.print_exc()


def test_widgets():
    """测试自定义控件"""
    try:
        from src.ui.widgets import ProgressDialog, StatusBar
        
        print("测试自定义控件...")
        
        root = tk.Tk()
        root.title("控件测试")
        root.geometry("400x200")
        
        # 测试状态栏
        status_bar = StatusBar(root)
        status_bar.set_status("控件测试中...")
        status_bar.set_progress("测试进度")
        
        # 测试按钮
        def test_progress():
            progress = ProgressDialog(root, "测试进度", "正在测试进度对话框...")
            root.after(2000, progress.close)  # 2秒后自动关闭
        
        tk.Button(root, text="测试进度对话框", command=test_progress).pack(pady=20)
        
        tk.Label(root, text="自定义控件测试", font=('Arial', 14)).pack(pady=20)
        
        print("自定义控件测试窗口已打开")
        root.mainloop()
        
    except Exception as e:
        print(f"控件测试失败：{str(e)}")
        import traceback
        traceback.print_exc()


def test_app_controller():
    """测试应用程序控制器"""
    try:
        print("测试应用程序控制器...")
        
        # 检查是否有测试数据
        test_data_files = [
            "data/dlt_data.csv",
            "data/ssq_data.csv", 
            "data/pl5_3000_data.csv"
        ]
        
        available_files = [f for f in test_data_files if os.path.exists(f)]
        
        if available_files:
            print(f"找到测试数据文件：{available_files}")
            
            from src.ui.app_controller import AppController
            
            print("创建应用程序控制器...")
            app = AppController()
            
            print("应用程序控制器创建成功！")
            print("功能包括：")
            print("- 数据导入和预览")
            print("- 多线程分析处理")
            print("- 结果显示和导出")
            print("- 错误处理和用户提示")
            print("\n启动完整应用程序...")
            
            # 运行完整应用程序
            app.run()
            
        else:
            print("未找到测试数据文件，仅测试界面...")
            test_main_window()
            
    except Exception as e:
        print(f"应用程序测试失败：{str(e)}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("=" * 50)
    print("彩票号码分析系统 UI 界面演示")
    print("=" * 50)
    
    print("\n选择测试模式：")
    print("1. 测试主窗口界面")
    print("2. 测试自定义控件")
    print("3. 测试完整应用程序")
    print("4. 退出")
    
    try:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == "1":
            test_main_window()
        elif choice == "2":
            test_widgets()
        elif choice == "3":
            test_app_controller()
        elif choice == "4":
            print("退出演示")
        else:
            print("无效选择，启动完整应用程序...")
            test_app_controller()
            
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出现错误：{str(e)}")


if __name__ == "__main__":
    main()
