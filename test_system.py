#!/usr/bin/env python3
"""
系统测试脚本

测试重构后的彩票分析系统的各项功能。
"""

import sys
import os
import logging
from datetime import datetime
import tempfile

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from src.services.lottery_service import LotteryService
from src.calculator.basic_calculator import BasicCalculator
from src.calculator.pattern_calculator import PatternCalculator
from src.database.models import LotteryRecord


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def test_basic_calculator():
    """测试基础计算器"""
    print("\n=== 测试基础计算器 ===")
    
    # 测试大乐透数据
    # 1(奇), 5(奇), 12(偶), 18(偶), 25(奇) -> 奇偶: 11001
    # 1(小), 5(小), 12(小), 18(小), 25(大) -> 大小: 00001
    dlt_front = [1, 5, 12, 18, 25]
    dlt_back = [3, 8]
    
    print(f"大乐透前区号码: {dlt_front}")
    
    # 测试奇偶计算
    odd_even_ratio = BasicCalculator.calculate_odd_even_ratio(dlt_front)
    odd_even_pattern = BasicCalculator.generate_odd_even_pattern(dlt_front)
    print(f"奇偶比: {odd_even_ratio}")
    print(f"奇偶排布: {odd_even_pattern}")
    
    # 测试大小计算
    big_small_ratio = BasicCalculator.calculate_big_small_ratio(dlt_front, 'dlt', 'front')
    big_small_pattern = BasicCalculator.generate_big_small_pattern(dlt_front, 'dlt', 'front')
    print(f"大小比: {big_small_ratio}")
    print(f"大小排布: {big_small_pattern}")
    
    # 测试分区模式
    zone_pattern = BasicCalculator.generate_zone_pattern(dlt_front, 'dlt', 'front')
    print(f"分区排布: {zone_pattern}")
    
    # 测试十进制转换
    odd_even_decimal = BasicCalculator.pattern_to_decimal(odd_even_pattern)
    zone_decimal = BasicCalculator.pattern_to_decimal(zone_pattern)
    print(f"奇偶排布十进制: {odd_even_decimal}")
    print(f"分区排布十进制: {zone_decimal}")
    
    # 验证计算结果
    assert odd_even_pattern == "11001", f"奇偶排布错误: 期望 11001, 实际 {odd_even_pattern}"
    assert big_small_pattern == "00001", f"大小排布错误: 期望 00001, 实际 {big_small_pattern}"
    assert odd_even_decimal == 25, f"奇偶十进制错误: 期望 25, 实际 {odd_even_decimal}"
    
    print("✅ 基础计算器测试通过")


def test_pattern_calculator():
    """测试模式计算器"""
    print("\n=== 测试模式计算器 ===")
    
    # 创建测试记录
    records = []
    
    # 记录1: 2024-01-01, 奇偶排布 11001
    record1 = LotteryRecord()
    record1.lottery_type = 'dlt'
    record1.issue_number = '24001'
    record1.draw_date = datetime(2024, 1, 1)
    record1.front_numbers = [1, 5, 12, 18, 25]
    record1.back_numbers = [3, 8]
    record1.odd_even_pattern = "11001"
    record1.big_small_pattern = "00001"
    records.append(record1)
    
    # 记录2: 2024-01-03, 奇偶排布 01101
    record2 = LotteryRecord()
    record2.lottery_type = 'dlt'
    record2.issue_number = '24002'
    record2.draw_date = datetime(2024, 1, 3)
    record2.front_numbers = [2, 7, 11, 19, 24]
    record2.back_numbers = [5, 10]
    record2.odd_even_pattern = "01101"
    record2.big_small_pattern = "00110"
    records.append(record2)
    
    # 记录3: 2024-01-05, 奇偶排布 11001 (与记录1相同)
    record3 = LotteryRecord()
    record3.lottery_type = 'dlt'
    record3.issue_number = '24003'
    record3.draw_date = datetime(2024, 1, 5)
    record3.front_numbers = [3, 6, 13, 20, 27]  # 3(奇), 6(偶), 13(奇), 20(偶), 27(奇) -> 10101
    record3.back_numbers = [1, 9]
    record3.odd_even_pattern = "11001"  # 使用与record1相同的模式进行测试
    record3.big_small_pattern = "10111"
    records.append(record3)
    
    # 测试查找上次相同模式
    last_date, interval = PatternCalculator.find_last_same_pattern(record3, records, 'odd_even')
    
    print(f"记录3的奇偶排布: {record3.odd_even_pattern}")
    print(f"上次相同模式日期: {last_date}")
    print(f"间隔期数: {interval}")
    
    # 验证结果
    assert last_date == record1.draw_date, f"上次日期错误: 期望 {record1.draw_date}, 实际 {last_date}"
    assert interval == 2, f"间隔期数错误: 期望 2, 实际 {interval}"
    
    print("✅ 模式计算器测试通过")


def test_lottery_service():
    """测试彩票服务"""
    print("\n=== 测试彩票服务 ===")
    
    # 使用临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        db_path = tmp_file.name
    
    try:
        service = LotteryService(db_path)
        
        # 测试添加单条记录
        record = service.add_single_record(
            issue_number='24001',
            draw_date=datetime(2024, 1, 1),
            front_numbers=[1, 5, 12, 18, 25],
            back_numbers=[3, 8],
            lottery_type='dlt'
        )
        
        print(f"添加记录: {record.issue_number}")
        print(f"奇偶比: {record.odd_even_ratio}")
        print(f"奇偶排布: {record.odd_even_pattern}")
        print(f"大小比: {record.big_small_ratio}")
        print(f"大小排布: {record.big_small_pattern}")
        
        # 验证计算结果
        assert record.odd_even_ratio == "3:2", f"奇偶比错误: {record.odd_even_ratio}"
        assert record.odd_even_pattern == "11001", f"奇偶排布错误: {record.odd_even_pattern}"
        
        # 添加第二条记录
        record2 = service.add_single_record(
            issue_number='24002',
            draw_date=datetime(2024, 1, 3),
            front_numbers=[1, 5, 12, 18, 25],  # 相同的奇偶排布
            back_numbers=[5, 10],
            lottery_type='dlt'
        )
        
        print(f"\n添加记录2: {record2.issue_number}")
        print(f"上次奇偶排布: {record2.format_last_odd_even_pattern()}")
        
        # 验证上次模式
        assert record2.last_odd_even_pattern_interval == 1, f"上次奇偶间隔错误: {record2.last_odd_even_pattern_interval}"
        
        # 测试查询功能
        records = service.get_records('dlt')
        assert len(records) == 2, f"记录数量错误: 期望 2, 实际 {len(records)}"
        
        # 测试统计功能
        stats = service.get_statistics('dlt')
        assert stats['dlt']['count'] == 2, f"统计数量错误: {stats}"
        
        service.close()
        print("✅ 彩票服务测试通过")
        
    finally:
        # 清理临时文件
        if os.path.exists(db_path):
            os.unlink(db_path)


def test_data_migration():
    """测试数据迁移功能"""
    print("\n=== 测试数据迁移功能 ===")
    
    # 创建测试数据文件（CSV格式）
    # 确保第1和第3条记录有相同的奇偶排布
    test_data = """期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
24001,2024-01-01,1,5,12,18,25,3,8
24002,2024-01-03,2,7,11,19,24,5,10
24003,2024-01-05,1,5,12,18,25,1,9"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as tmp_file:
        tmp_file.write(test_data)
        data_file = tmp_file.name
    
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
        db_path = tmp_db.name
    
    try:
        service = LotteryService(db_path)
        
        # 测试数据导入
        count = service.import_data_from_file(data_file, 'dlt')
        print(f"导入记录数量: {count}")
        
        # 验证导入结果
        records = service.get_records('dlt', order_by="draw_date ASC")
        assert len(records) == 3, f"导入记录数量错误: 期望 3, 实际 {len(records)}"
        
        # 验证第三条记录的上次模式计算
        last_record = records[-1]
        print(f"最后一条记录: {last_record.issue_number}")
        print(f"奇偶排布: {last_record.odd_even_pattern}")
        print(f"上次奇偶排布: {last_record.format_last_odd_even_pattern()}")
        
        # 验证上次模式是否正确计算
        assert last_record.last_odd_even_pattern_interval is not None, "上次奇偶间隔未计算"
        
        service.close()
        print("✅ 数据迁移测试通过")
        
    finally:
        # 清理临时文件
        if os.path.exists(data_file):
            os.unlink(data_file)
        if os.path.exists(db_path):
            os.unlink(db_path)


def test_ui_integration():
    """测试UI集成"""
    print("\n=== 测试UI集成 ===")

    try:
        # 测试导入是否正常
        from src.ui.simple_main_window import SimpleMainWindow
        from src.services.lottery_service import LotteryService

        print("UI模块导入成功")
        print("✅ UI集成测试通过")

    except Exception as e:
        print(f"❌ UI集成测试失败: {e}")


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始系统测试...")
    
    try:
        test_basic_calculator()
        test_pattern_calculator()
        test_lottery_service()
        test_data_migration()
        test_ui_integration()
        
        print("\n🎉 所有测试通过！")
        print("\n=== 系统功能验证完成 ===")
        print("✅ 数据库存储功能正常")
        print("✅ 基础计算功能正常")
        print("✅ 上次模式计算功能正常")
        print("✅ 数据导入功能正常")
        print("✅ UI界面集成正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    setup_logging()
    
    success = run_all_tests()
    
    if success:
        print("\n🎯 系统重构完成，所有功能验证通过！")
        print("\n📋 使用说明:")
        print("1. 运行 'python run_simple_app.py' 启动简化版界面")
        print("2. 使用界面的'导入数据'功能导入彩票数据文件")
        print("3. 查看表格中的新列：上次奇偶排布、上次大小排布")
        return 0
    else:
        print("\n❌ 系统测试失败，请检查错误信息")
        return 1


if __name__ == "__main__":
    sys.exit(main())
