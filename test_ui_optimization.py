#!/usr/bin/env python3
"""
测试UI布局优化效果
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_ui_layout():
    """测试UI布局优化"""
    print("🎨 测试UI布局优化效果")
    print("=" * 60)
    
    try:
        from src.ui.main_window import MainWindow
        
        # 创建主窗口
        print("🏠 创建主窗口...")
        main_window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 检查工具栏按钮
        print("\n🔧 检查工具栏按钮:")
        toolbar_children = main_window.toolbar_frame.winfo_children()
        button_texts = []
        
        for child in toolbar_children:
            if hasattr(child, 'cget'):
                try:
                    text = child.cget('text')
                    button_texts.append(text)
                except:
                    pass
        
        print(f"工具栏按钮: {button_texts}")
        
        # 验证按钮配置
        expected_buttons = ["导入数据", "导出结果", "清除结果"]
        removed_buttons = ["手动分析"]
        
        for btn in expected_buttons:
            if btn in button_texts:
                print(f"  ✅ {btn}: 存在")
            else:
                print(f"  ❌ {btn}: 缺失")
        
        for btn in removed_buttons:
            if btn not in button_texts:
                print(f"  ✅ {btn}: 已移除")
            else:
                print(f"  ❌ {btn}: 仍然存在")
        
        # 检查是否移除了底部操作按钮区域
        print("\n📋 检查布局结构:")
        if hasattr(main_window, 'action_frame'):
            print("  ⚠️  action_frame 属性仍然存在")
        else:
            print("  ✅ action_frame 属性已移除")
        
        # 检查布局顺序
        print("\n📐 检查布局顺序:")
        root_children = main_window.root.winfo_children()
        frame_names = []
        
        for child in root_children:
            if hasattr(child, '_name'):
                frame_names.append(child._name)
        
        print(f"界面组件顺序: {len(root_children)} 个组件")
        
        # 测试按钮功能
        print("\n🧪 测试按钮功能:")
        
        # 测试清除结果按钮
        if hasattr(main_window, 'clear_results'):
            print("  ✅ clear_results 方法存在")
        else:
            print("  ❌ clear_results 方法不存在")
        
        # 测试导入数据按钮
        if hasattr(main_window, 'import_data'):
            print("  ✅ import_data 方法存在")
        else:
            print("  ❌ import_data 方法不存在")
        
        # 测试导出结果按钮
        if hasattr(main_window, 'export_results'):
            print("  ✅ export_results 方法存在")
        else:
            print("  ❌ export_results 方法不存在")
        
        # 检查分析选项区域是否保留
        print("\n📊 检查分析选项区域:")
        if hasattr(main_window, 'options_frame'):
            print("  ✅ 分析选项区域保留")
            
            # 检查分析选项
            options_count = len(main_window.analysis_options)
            print(f"  ✅ 分析选项数量: {options_count}")
            
            for key, var in main_window.analysis_options.items():
                print(f"    - {key}: {var.get()}")
        else:
            print("  ❌ 分析选项区域缺失")
        
        main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_application():
    """测试完整应用程序"""
    print(f"\n🚀 测试完整应用程序")
    print("=" * 60)
    
    try:
        from src.ui.app_controller import AppController
        
        print("🎮 创建应用控制器...")
        controller = AppController()
        print("✅ 应用控制器创建成功")
        
        print("🔗 检查事件绑定...")
        if hasattr(controller.main_window, 'on_import_data'):
            if controller.main_window.on_import_data is not None:
                print("  ✅ 导入数据事件已绑定")
            else:
                print("  ❌ 导入数据事件未绑定")
        
        if hasattr(controller.main_window, 'on_clear_results'):
            if controller.main_window.on_clear_results is not None:
                print("  ✅ 清除结果事件已绑定")
            else:
                print("  ❌ 清除结果事件未绑定")
        
        controller.main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 应用程序测试失败: {e}")
        return False

def create_layout_summary():
    """创建布局优化总结"""
    print(f"\n📋 UI布局优化总结")
    print("=" * 60)
    print(f"🎯 优化内容:")
    print(f"  ✅ 删除工具栏中的'手动分析'按钮")
    print(f"  ✅ 在工具栏中添加'清除结果'按钮")
    print(f"  ✅ 删除底部的操作按钮区域")
    print(f"  ✅ 保留分析选项区域")
    print(f"")
    print(f"🎨 新的布局结构:")
    print(f"  1. 顶部工具栏: [导入数据] [导出结果] [清除结果]")
    print(f"  2. 彩票类型选择区")
    print(f"  3. 分析选项区")
    print(f"  4. 数据显示区 (表格 + 分析结果)")
    print(f"")
    print(f"💡 用户体验改进:")
    print(f"  - 所有主要操作按钮集中在顶部")
    print(f"  - 界面更加简洁，减少冗余")
    print(f"  - 清除结果按钮更容易访问")
    print(f"  - 移除了重复的手动分析功能")

def main():
    """主函数"""
    print("🎨 彩票分析系统 - UI布局优化测试")
    print("=" * 80)
    
    # 测试UI布局
    test1_success = test_ui_layout()
    
    # 测试完整应用程序
    test2_success = test_complete_application()
    
    # 显示总结
    create_layout_summary()
    
    print(f"\n📊 测试结果:")
    print(f"UI布局测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"应用程序测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 UI布局优化完成！")
        print(f"🚀 运行 python main.py 查看优化效果")
    else:
        print(f"\n❌ 仍有问题需要解决")

if __name__ == "__main__":
    main()
