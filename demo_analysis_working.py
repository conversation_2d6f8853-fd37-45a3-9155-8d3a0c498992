#!/usr/bin/env python3
"""
演示分析功能确实可以工作
"""

import sys
import os
import tempfile
import csv

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from src.data.reader import DataReader
from src.data.validator import DataValidator
from src.data.config import LOTTERY_TYPE_DLT
from src.analysis.dlt_analyzer import DLTAnalyzer


def create_test_data():
    """创建测试数据文件"""
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
    
    # 大乐透测试数据
    data = [
        ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"],
        ["24001", "2024-01-01", "1", "5", "15", "25", "35", "2", "8"],
        ["24002", "2024-01-03", "3", "7", "17", "27", "33", "1", "9"],
        ["24003", "2024-01-06", "2", "8", "18", "28", "34", "3", "10"],
        ["24004", "2024-01-08", "4", "9", "19", "29", "31", "5", "11"],
        ["24005", "2024-01-10", "6", "11", "21", "30", "32", "4", "12"],
        ["24006", "2024-01-13", "7", "12", "22", "26", "35", "6", "7"],
        ["24007", "2024-01-15", "1", "10", "20", "24", "33", "8", "9"],
        ["24008", "2024-01-17", "5", "13", "23", "27", "34", "2", "11"],
        ["24009", "2024-01-20", "3", "14", "16", "28", "32", "1", "12"],
        ["24010", "2024-01-22", "8", "15", "25", "29", "31", "4", "6"]
    ]
    
    writer = csv.writer(temp_file)
    for row in data:
        writer.writerow(row)
    
    temp_file.close()
    return temp_file.name


def demo_analysis():
    """演示分析功能"""
    print("=" * 80)
    print("彩票号码分析系统 - 分析功能演示")
    print("=" * 80)
    
    try:
        # 1. 创建测试数据
        test_file = create_test_data()
        print(f"✅ 创建测试数据文件: {test_file}")
        
        # 2. 读取数据
        data_reader = DataReader()
        data_list = data_reader.read_csv(test_file, LOTTERY_TYPE_DLT)
        print(f"✅ 成功读取 {len(data_list)} 条数据")
        
        # 3. 验证数据
        data_validator = DataValidator()
        validation_result = data_validator.validate_data_list(data_list)
        print(f"✅ 数据验证: {validation_result['valid_count']}/{validation_result['total_count']} 条有效")
        
        # 4. 创建分析器
        analyzer = DLTAnalyzer()
        analyzer.load_data(data_list)
        print("✅ 分析器加载数据成功")
        
        # 5. 执行分析
        print("\n" + "=" * 80)
        print("开始执行分析...")
        print("=" * 80)
        
        # 奇偶分析
        print("\n【奇偶分析】")
        print("-" * 60)
        odd_even_front = analyzer.analyze_odd_even_distribution('front')
        print(f"前区奇偶分析：")
        print(f"  总期数：{odd_even_front['total_periods']}")
        print(f"  最新模式：{odd_even_front['latest_pattern']}")
        
        if 'pattern_statistics' in odd_even_front:
            print("  模式统计（前3位）：")
            pattern_stats = odd_even_front['pattern_statistics']
            sorted_patterns = sorted(pattern_stats.items(), key=lambda x: x[1]['count'], reverse=True)
            for i, (pattern, stats) in enumerate(sorted_patterns[:3]):
                print(f"    {pattern}: {stats['count']}次 ({stats['frequency']:.1%})")
        
        odd_even_back = analyzer.analyze_odd_even_distribution('back')
        print(f"后区奇偶分析：")
        print(f"  总期数：{odd_even_back['total_periods']}")
        print(f"  最新模式：{odd_even_back['latest_pattern']}")
        
        # 大小分析
        print("\n【大小分析】")
        print("-" * 60)
        big_small_front = analyzer.analyze_big_small_distribution('front')
        print(f"前区大小分析：")
        print(f"  总期数：{big_small_front['total_periods']}")
        print(f"  最新模式：{big_small_front['latest_pattern']}")
        
        big_small_back = analyzer.analyze_big_small_distribution('back')
        print(f"后区大小分析：")
        print(f"  总期数：{big_small_back['total_periods']}")
        print(f"  最新模式：{big_small_back['latest_pattern']}")
        
        # 遗漏分析
        print("\n【遗漏分析】")
        print("-" * 60)
        missing_front = analyzer.calculate_all_numbers_missing('front')
        print(f"前区遗漏分析：")
        print(f"  总号码数：{missing_front['total_numbers']}")
        print(f"  总期数：{missing_front['total_periods']}")
        
        if missing_front.get('most_missing'):
            most_missing = missing_front['most_missing']
            print(f"  最大遗漏：号码{most_missing['number']} (遗漏{most_missing['missing']}期)")
        
        missing_back = analyzer.calculate_all_numbers_missing('back')
        print(f"后区遗漏分析：")
        print(f"  总号码数：{missing_back['total_numbers']}")
        print(f"  总期数：{missing_back['total_periods']}")
        
        # 分区比分析
        print("\n【分区比分析】")
        print("-" * 60)
        zone_front = analyzer.analyze_zone_ratio('front')
        print(f"前区分区比分析：")
        print(f"  总期数：{zone_front['total_periods']}")
        print(f"  最新模式：{zone_front['latest_pattern']}")
        
        zone_back = analyzer.analyze_zone_ratio('back')
        print(f"后区分区比分析：")
        print(f"  总期数：{zone_back['total_periods']}")
        print(f"  最新模式：{zone_back['latest_pattern']}")
        
        # 清理
        os.unlink(test_file)
        
        print("\n" + "=" * 80)
        print("🎉 分析功能演示完成！所有功能正常工作。")
        print("=" * 80)
        
        print("\n📋 功能确认:")
        print("  ✅ 数据导入和验证")
        print("  ✅ 奇偶分析（前区/后区）")
        print("  ✅ 大小分析（前区/后区）")
        print("  ✅ 号码遗漏分析（前区/后区）")
        print("  ✅ 分区比分析（前区/后区）")
        print("  ✅ 结果格式化输出")
        
        print("\n🚀 使用说明:")
        print("  1. 运行: python main.py")
        print("  2. 选择彩票类型（大乐透）")
        print("  3. 点击'导入数据'选择CSV文件")
        print("  4. 选择分析选项")
        print("  5. 点击'开始分析'")
        print("  6. 查看分析结果")
        
        print("\n💡 注意事项:")
        print("  - 确保CSV文件格式正确")
        print("  - 数据文件需要包含列名")
        print("  - 建议数据量不少于10期")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    demo_analysis()
