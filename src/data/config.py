"""
彩票规则配置

定义不同彩票类型的分析规则和参数配置。
包括号码范围、大小分界点、分区定义等。
"""

from typing import Dict, List, Tuple, Any


# 彩票类型常量
LOTTERY_TYPE_DLT = 'dlt'  # 大乐透
LOTTERY_TYPE_SSQ = 'ssq'  # 双色球
LOTTERY_TYPE_PL5 = 'pl5'  # 排列五

# 彩票规则配置字典
LOTTERY_RULES: Dict[str, Dict[str, Any]] = {
    # 大乐透规则配置
    LOTTERY_TYPE_DLT: {
        # 号码范围 [前区范围, 后区范围]
        "number_ranges": [35, 12],
        
        # 前区和后区的号码数量
        "front_count": 5,
        "back_count": 2,
        
        # 大小分界点
        "big_small_boundary": {
            "front": 18,  # 前区大于18为大号
            "back": 7     # 后区大于7为大号
        },
        
        # 分区定义 (前区1-35分为7个区)
        "zones": [
            {"name": "zone1", "range": (1, 5), "description": "第一区(01-05)"},
            {"name": "zone2", "range": (6, 10), "description": "第二区(06-10)"},
            {"name": "zone3", "range": (11, 15), "description": "第三区(11-15)"},
            {"name": "zone4", "range": (16, 20), "description": "第四区(16-20)"},
            {"name": "zone5", "range": (21, 25), "description": "第五区(21-25)"},
            {"name": "zone6", "range": (26, 30), "description": "第六区(26-30)"},
            {"name": "zone7", "range": (31, 35), "description": "第七区(31-35)"}
        ],
        
        # 后区分区定义
        "back_zones": [
            {"name": "back_zone1", "range": (1, 6), "description": "后区小号(01-06)"},
            {"name": "back_zone2", "range": (7, 12), "description": "后区大号(07-12)"}
        ],
        
        # 开奖频率
        "draw_frequency": "每周一、三、六",
        
        # 彩票名称
        "display_name": "大乐透"
    },
    
    # 双色球规则配置
    LOTTERY_TYPE_SSQ: {
        # 号码范围 [红球范围, 蓝球范围]
        "number_ranges": [33, 33],
        
        # 红球和蓝球的号码数量
        "front_count": 6,  # 红球数量
        "back_count": 1,   # 蓝球数量
        
        # 大小分界点
        "big_small_boundary": {
            "front": 17,  # 红球大于17为大号
            "back": 9     # 蓝球大于9为大号
        },
        
        # 分区定义 (红球1-33分为7个区)
        "zones": [
            {"name": "zone1", "range": (1, 5), "description": "第一区(01-05)"},
            {"name": "zone2", "range": (6, 10), "description": "第二区(06-10)"},
            {"name": "zone3", "range": (11, 15), "description": "第三区(11-15)"},
            {"name": "zone4", "range": (16, 20), "description": "第四区(16-20)"},
            {"name": "zone5", "range": (21, 25), "description": "第五区(21-25)"},
            {"name": "zone6", "range": (26, 30), "description": "第六区(26-30)"},
            {"name": "zone7", "range": (31, 33), "description": "第七区(31-33)"}
        ],
        
        # 蓝球分区定义
        "back_zones": [
            {"name": "back_zone1", "range": (1, 8), "description": "蓝球小号(01-08)"},
            {"name": "back_zone2", "range": (9, 16), "description": "蓝球大号(09-16)"}
        ],
        
        # 开奖频率
        "draw_frequency": "每周二、四、日",
        
        # 彩票名称
        "display_name": "双色球"
    },
    
    # 排列五规则配置
    LOTTERY_TYPE_PL5: {
        # 号码范围 (每位数字0-9)
        "number_ranges": [10, 10, 10, 10, 10],
        
        # 位数
        "digit_count": 5,
        
        # 大小分界点 (大于等于5为大号)
        "big_small_boundary": 5,
        
        # 分区定义 (每位数字分为2个区)
        "zones": [
            {"name": "zone1", "range": (0, 4), "description": "小号区(0-4)"},
            {"name": "zone2", "range": (5, 9), "description": "大号区(5-9)"}
        ],
        
        # 开奖频率
        "draw_frequency": "每天开奖",
        
        # 彩票名称
        "display_name": "排列五"
    }
}


def get_lottery_rule(lottery_type: str, rule_key: str = None) -> Any:
    """
    获取指定彩票类型的规则配置
    
    Args:
        lottery_type (str): 彩票类型 ('dlt', 'ssq', 'pl5')
        rule_key (str, optional): 具体的规则键名，如果为None则返回全部规则
        
    Returns:
        Any: 规则配置值
        
    Raises:
        ValueError: 当彩票类型不存在时抛出异常
    """
    if lottery_type not in LOTTERY_RULES:
        raise ValueError(f"不支持的彩票类型: {lottery_type}")
    
    rules = LOTTERY_RULES[lottery_type]
    
    if rule_key is None:
        return rules
    
    if rule_key not in rules:
        raise ValueError(f"规则键 '{rule_key}' 在彩票类型 '{lottery_type}' 中不存在")
    
    return rules[rule_key]


def get_number_range(lottery_type: str, zone_type: str = 'front') -> int:
    """
    获取指定彩票类型的号码范围
    
    Args:
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型 ('front' 或 'back')
        
    Returns:
        int: 号码范围的最大值
    """
    rules = get_lottery_rule(lottery_type)
    
    if lottery_type == LOTTERY_TYPE_PL5:
        return rules["number_ranges"][0]  # 排列五每位都是0-9
    
    if zone_type == 'front':
        return rules["number_ranges"][0]
    elif zone_type == 'back':
        return rules["number_ranges"][1]
    else:
        raise ValueError(f"不支持的区域类型: {zone_type}")


def get_big_small_boundary(lottery_type: str, zone_type: str = 'front') -> int:
    """
    获取大小分界点
    
    Args:
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型 ('front' 或 'back')
        
    Returns:
        int: 大小分界点
    """
    rules = get_lottery_rule(lottery_type)
    
    if lottery_type == LOTTERY_TYPE_PL5:
        return rules["big_small_boundary"]
    
    boundary = rules["big_small_boundary"]
    
    if zone_type == 'front':
        return boundary["front"]
    elif zone_type == 'back':
        return boundary["back"]
    else:
        raise ValueError(f"不支持的区域类型: {zone_type}")


def get_zones(lottery_type: str, zone_type: str = 'front') -> List[Dict[str, Any]]:
    """
    获取分区定义
    
    Args:
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型 ('front' 或 'back')
        
    Returns:
        List[Dict[str, Any]]: 分区定义列表
    """
    rules = get_lottery_rule(lottery_type)
    
    if zone_type == 'front':
        return rules["zones"]
    elif zone_type == 'back':
        return rules.get("back_zones", [])
    else:
        raise ValueError(f"不支持的区域类型: {zone_type}")


def validate_numbers(lottery_type: str, numbers: List[int]) -> bool:
    """
    验证号码是否符合彩票规则
    
    Args:
        lottery_type (str): 彩票类型
        numbers (List[int]): 号码列表
        
    Returns:
        bool: 验证结果
    """
    rules = get_lottery_rule(lottery_type)
    
    if lottery_type == LOTTERY_TYPE_DLT:
        # 大乐透：5个前区号码 + 2个后区号码
        if len(numbers) != 7:
            return False
        front_numbers = numbers[:5]
        back_numbers = numbers[5:7]
        
        # 验证前区号码范围
        for num in front_numbers:
            if not (1 <= num <= rules["number_ranges"][0]):
                return False
        
        # 验证后区号码范围
        for num in back_numbers:
            if not (1 <= num <= rules["number_ranges"][1]):
                return False
        
        return True
    
    elif lottery_type == LOTTERY_TYPE_SSQ:
        # 双色球：6个红球 + 1个蓝球
        if len(numbers) != 7:
            return False
        red_numbers = numbers[:6]
        blue_number = numbers[6]

        # 验证红球号码范围
        for num in red_numbers:
            if not (1 <= num <= rules["number_ranges"][0]):
                return False

        # 验证蓝球号码范围
        if not (1 <= blue_number <= rules["number_ranges"][1]):
            return False

        return True
    
    elif lottery_type == LOTTERY_TYPE_PL5:
        # 排列五：5位数字
        if len(numbers) != 5:
            return False
        
        # 验证每位数字范围
        for num in numbers:
            if not (0 <= num <= 9):
                return False
        
        return True
    
    return False
