"""
彩票数据模型定义

定义彩票数据的统一数据结构，支持大乐透、双色球、排列五三种彩票类型。
提供数据验证、转换和格式化功能。
"""

from datetime import datetime
from typing import List, Union, Optional
import re


class LotteryData:
    """
    彩票数据模型类
    
    统一的彩票数据结构，支持不同类型彩票的数据存储和处理。
    """
    
    def __init__(self, issue_number: str = "", draw_date: Union[str, datetime] = "", 
                 numbers: List[int] = None, lottery_type: str = ""):
        """
        初始化彩票数据对象
        
        Args:
            issue_number (str): 期号
            draw_date (Union[str, datetime]): 开奖日期
            numbers (List[int]): 开奖号码列表
            lottery_type (str): 彩票类型 ('dlt', 'ssq', 'pl5')
        """
        self.issue_number = issue_number
        self.draw_date = self._parse_date(draw_date)
        self.numbers = numbers if numbers is not None else []
        self.lottery_type = lottery_type.lower()
        
        # 验证数据
        self._validate_data()
    
    def _parse_date(self, date_input: Union[str, datetime]) -> datetime:
        """
        解析日期字符串或datetime对象
        
        Args:
            date_input: 日期输入，可以是字符串或datetime对象
            
        Returns:
            datetime: 解析后的日期对象
        """
        if isinstance(date_input, datetime):
            return date_input
        elif isinstance(date_input, str):
            if not date_input:
                return datetime.now()
            
            # 尝试多种日期格式
            date_formats = [
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%Y.%m.%d',
                '%Y-%m-%d %H:%M:%S'
            ]
            
            for fmt in date_formats:
                try:
                    return datetime.strptime(date_input, fmt)
                except ValueError:
                    continue
            
            raise ValueError(f"无法解析日期格式: {date_input}")
        else:
            raise TypeError("日期必须是字符串或datetime对象")
    
    def _validate_data(self):
        """
        验证数据的有效性
        
        Raises:
            ValueError: 当数据不符合要求时抛出异常
        """
        # 验证期号格式
        if self.issue_number and not re.match(r'^\d+$', str(self.issue_number)):
            raise ValueError(f"期号格式错误: {self.issue_number}")
        
        # 验证彩票类型
        valid_types = ['dlt', 'ssq', 'pl5', '']
        if self.lottery_type not in valid_types:
            raise ValueError(f"不支持的彩票类型: {self.lottery_type}")
        
        # 验证号码列表
        if self.numbers:
            if not isinstance(self.numbers, list):
                raise ValueError("号码必须是列表格式")
            
            for num in self.numbers:
                if not isinstance(num, int) or num < 0:
                    raise ValueError(f"号码必须是非负整数: {num}")
    
    def get_front_numbers(self) -> List[int]:
        """
        获取前区号码（适用于大乐透和双色球）
        
        Returns:
            List[int]: 前区号码列表
        """
        if self.lottery_type == 'dlt':
            return self.numbers[:5] if len(self.numbers) >= 5 else []
        elif self.lottery_type == 'ssq':
            return self.numbers[:6] if len(self.numbers) >= 6 else []
        else:
            return self.numbers
    
    def get_back_numbers(self) -> List[int]:
        """
        获取后区号码（适用于大乐透和双色球）
        
        Returns:
            List[int]: 后区号码列表
        """
        if self.lottery_type == 'dlt':
            return self.numbers[5:7] if len(self.numbers) >= 7 else []
        elif self.lottery_type == 'ssq':
            return self.numbers[6:7] if len(self.numbers) >= 7 else []
        else:
            return []
    
    def to_dict(self) -> dict:
        """
        将对象转换为字典格式
        
        Returns:
            dict: 包含所有属性的字典
        """
        return {
            'issue_number': self.issue_number,
            'draw_date': self.draw_date.strftime('%Y-%m-%d') if self.draw_date else '',
            'numbers': self.numbers,
            'lottery_type': self.lottery_type,
            'front_numbers': self.get_front_numbers(),
            'back_numbers': self.get_back_numbers()
        }
    
    def __str__(self) -> str:
        """
        字符串表示
        
        Returns:
            str: 对象的字符串描述
        """
        date_str = self.draw_date.strftime('%Y-%m-%d') if self.draw_date else '未知'
        return f"期号: {self.issue_number}, 日期: {date_str}, 号码: {self.numbers}, 类型: {self.lottery_type}"
    
    def __repr__(self) -> str:
        """
        对象表示
        
        Returns:
            str: 对象的详细表示
        """
        return f"LotteryData(issue_number='{self.issue_number}', draw_date='{self.draw_date}', numbers={self.numbers}, lottery_type='{self.lottery_type}')"


def create_lottery_data(issue_number: str, draw_date: str, numbers: List[int], 
                       lottery_type: str) -> LotteryData:
    """
    创建彩票数据对象的工厂函数
    
    Args:
        issue_number (str): 期号
        draw_date (str): 开奖日期
        numbers (List[int]): 开奖号码列表
        lottery_type (str): 彩票类型
        
    Returns:
        LotteryData: 彩票数据对象
    """
    return LotteryData(issue_number, draw_date, numbers, lottery_type)
