"""
彩票数据读取器

实现CSV文件读取功能，支持解析大乐透、双色球、排列五三种不同格式的数据文件。
处理不同彩票类型的数据格式差异，构建统一的数据结构。
"""

import pandas as pd
import os
from typing import List, Optional, Union
import logging
from .models import LotteryData, create_lottery_data
from .config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5, validate_numbers

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataReader:
    """
    彩票数据读取器类
    
    支持读取和解析大乐透、双色球、排列五三种彩票类型的CSV数据文件。
    """
    
    def __init__(self):
        """初始化数据读取器"""
        self.supported_types = [LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5]
    
    def read_csv(self, file_path: str, lottery_type: str, 
                 encoding: str = 'utf-8') -> List[LotteryData]:
        """
        读取CSV文件并解析为LotteryData对象列表
        
        Args:
            file_path (str): CSV文件路径
            lottery_type (str): 彩票类型 ('dlt', 'ssq', 'pl5')
            encoding (str): 文件编码，默认为utf-8
            
        Returns:
            List[LotteryData]: 彩票数据对象列表
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 不支持的彩票类型或数据格式错误
            Exception: 其他读取错误
        """
        # 验证彩票类型
        if lottery_type not in self.supported_types:
            raise ValueError(f"不支持的彩票类型: {lottery_type}")
        
        # 验证文件存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            logger.info(f"开始读取文件: {file_path}, 彩票类型: {lottery_type}")
            
            # 尝试不同编码读取文件
            encodings = [encoding, 'utf-8', 'gbk', 'gb2312']
            df = None
            
            for enc in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=enc)
                    logger.info(f"成功使用编码 {enc} 读取文件")
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                raise Exception(f"无法使用任何编码读取文件: {file_path}")
            
            # 根据彩票类型解析数据
            if lottery_type == LOTTERY_TYPE_DLT:
                return self.parse_dlt_data(df)
            elif lottery_type == LOTTERY_TYPE_SSQ:
                return self.parse_ssq_data(df)
            elif lottery_type == LOTTERY_TYPE_PL5:
                return self.parse_pl5_data(df)
            
        except Exception as e:
            logger.error(f"读取文件失败: {file_path}, 错误: {str(e)}")
            raise
    
    def parse_dlt_data(self, df: pd.DataFrame) -> List[LotteryData]:
        """
        解析大乐透数据
        
        Args:
            df (pd.DataFrame): 原始数据DataFrame
            
        Returns:
            List[LotteryData]: 大乐透数据对象列表
        """
        logger.info(f"开始解析大乐透数据，共 {len(df)} 条记录")
        
        # 验证列名
        expected_columns = ['期号', '开奖日期', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        if not all(col in df.columns for col in expected_columns):
            raise ValueError(f"大乐透数据格式错误，期望列名: {expected_columns}")
        
        lottery_data_list = []
        error_count = 0
        
        for index, row in df.iterrows():
            try:
                # 提取基本信息
                issue_number = str(row['期号']).strip()
                draw_date = str(row['开奖日期']).strip()
                
                # 提取号码（前区5个 + 后区2个）
                numbers = [
                    int(row['红球1']), int(row['红球2']), int(row['红球3']), 
                    int(row['红球4']), int(row['红球5']),
                    int(row['蓝球1']), int(row['蓝球2'])
                ]
                
                # 验证号码格式
                if not validate_numbers(LOTTERY_TYPE_DLT, numbers):
                    logger.warning(f"第 {index+1} 行数据验证失败: {numbers}")
                    error_count += 1
                    continue
                
                # 创建LotteryData对象
                lottery_data = create_lottery_data(
                    issue_number=issue_number,
                    draw_date=draw_date,
                    numbers=numbers,
                    lottery_type=LOTTERY_TYPE_DLT
                )
                
                lottery_data_list.append(lottery_data)
                
            except Exception as e:
                logger.warning(f"解析第 {index+1} 行数据失败: {str(e)}")
                error_count += 1
                continue
        
        logger.info(f"大乐透数据解析完成，成功: {len(lottery_data_list)} 条，失败: {error_count} 条")
        return lottery_data_list
    
    def parse_ssq_data(self, df: pd.DataFrame) -> List[LotteryData]:
        """
        解析双色球数据
        
        Args:
            df (pd.DataFrame): 原始数据DataFrame
            
        Returns:
            List[LotteryData]: 双色球数据对象列表
        """
        logger.info(f"开始解析双色球数据，共 {len(df)} 条记录")
        
        # 验证列名
        expected_columns = ['期号', '开奖日期', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球1']
        if not all(col in df.columns for col in expected_columns):
            raise ValueError(f"双色球数据格式错误，期望列名: {expected_columns}")

        lottery_data_list = []
        error_count = 0

        for index, row in df.iterrows():
            try:
                # 提取基本信息
                issue_number = str(row['期号']).strip()
                draw_date = str(row['开奖日期']).strip()

                # 提取号码（红球6个 + 蓝球1个）
                numbers = [
                    int(row['红球1']), int(row['红球2']), int(row['红球3']),
                    int(row['红球4']), int(row['红球5']), int(row['红球6']), int(row['蓝球1'])
                ]
                
                # 验证号码格式
                if not validate_numbers(LOTTERY_TYPE_SSQ, numbers):
                    logger.warning(f"第 {index+1} 行数据验证失败: {numbers}")
                    error_count += 1
                    continue
                
                # 创建LotteryData对象
                lottery_data = create_lottery_data(
                    issue_number=issue_number,
                    draw_date=draw_date,
                    numbers=numbers,
                    lottery_type=LOTTERY_TYPE_SSQ
                )
                
                lottery_data_list.append(lottery_data)
                
            except Exception as e:
                logger.warning(f"解析第 {index+1} 行数据失败: {str(e)}")
                error_count += 1
                continue
        
        logger.info(f"双色球数据解析完成，成功: {len(lottery_data_list)} 条，失败: {error_count} 条")
        return lottery_data_list

    def parse_pl5_data(self, df: pd.DataFrame) -> List[LotteryData]:
        """
        解析排列五数据（特殊格式处理）

        Args:
            df (pd.DataFrame): 原始数据DataFrame

        Returns:
            List[LotteryData]: 排列五数据对象列表
        """
        logger.info(f"开始解析排列五数据，共 {len(df)} 条记录")

        # 验证列名
        expected_columns = ['date', 'period', 'numbers', 'sum_value']
        if not all(col in df.columns for col in expected_columns):
            raise ValueError(f"排列五数据格式错误，期望列名: {expected_columns}")

        lottery_data_list = []
        error_count = 0

        for index, row in df.iterrows():
            try:
                # 提取基本信息
                issue_number = str(row['period']).strip()
                draw_date = str(row['date']).strip()

                # 解析号码字符串（空格分隔）
                numbers_str = str(row['numbers']).strip()
                numbers = [int(x) for x in numbers_str.split()]

                # 验证号码数量和范围
                if len(numbers) != 5:
                    logger.warning(f"第 {index+1} 行排列五号码数量错误: {numbers}")
                    error_count += 1
                    continue

                # 验证号码格式
                if not validate_numbers(LOTTERY_TYPE_PL5, numbers):
                    logger.warning(f"第 {index+1} 行数据验证失败: {numbers}")
                    error_count += 1
                    continue

                # 创建LotteryData对象
                lottery_data = create_lottery_data(
                    issue_number=issue_number,
                    draw_date=draw_date,
                    numbers=numbers,
                    lottery_type=LOTTERY_TYPE_PL5
                )

                lottery_data_list.append(lottery_data)

            except Exception as e:
                logger.warning(f"解析第 {index+1} 行数据失败: {str(e)}")
                error_count += 1
                continue

        logger.info(f"排列五数据解析完成，成功: {len(lottery_data_list)} 条，失败: {error_count} 条")
        return lottery_data_list

    def get_file_info(self, file_path: str) -> dict:
        """
        获取文件基本信息

        Args:
            file_path (str): 文件路径

        Returns:
            dict: 文件信息字典
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        try:
            # 尝试读取文件头部信息
            df = pd.read_csv(file_path, nrows=5, encoding='utf-8')

            return {
                'file_path': file_path,
                'file_size': os.path.getsize(file_path),
                'columns': list(df.columns),
                'sample_data': df.to_dict('records')
            }
        except Exception as e:
            logger.error(f"获取文件信息失败: {str(e)}")
            return {
                'file_path': file_path,
                'file_size': os.path.getsize(file_path),
                'error': str(e)
            }

    def detect_lottery_type(self, file_path: str) -> Optional[str]:
        """
        自动检测彩票类型

        Args:
            file_path (str): 文件路径

        Returns:
            Optional[str]: 检测到的彩票类型，如果无法检测则返回None
        """
        try:
            df = pd.read_csv(file_path, nrows=1, encoding='utf-8')
            columns = list(df.columns)

            # 根据列名判断彩票类型
            if '红球1' in columns and '蓝球2' in columns:
                return LOTTERY_TYPE_DLT
            elif '红球1' in columns and '蓝球1' in columns and '蓝球2' not in columns:
                return LOTTERY_TYPE_SSQ
            elif 'numbers' in columns and 'period' in columns:
                return LOTTERY_TYPE_PL5
            else:
                return None

        except Exception as e:
            logger.error(f"检测彩票类型失败: {str(e)}")
            return None


def read_lottery_data(file_path: str, lottery_type: str = None) -> List[LotteryData]:
    """
    便捷函数：读取彩票数据

    Args:
        file_path (str): 文件路径
        lottery_type (str, optional): 彩票类型，如果为None则自动检测

    Returns:
        List[LotteryData]: 彩票数据对象列表
    """
    reader = DataReader()

    # 自动检测彩票类型
    if lottery_type is None:
        lottery_type = reader.detect_lottery_type(file_path)
        if lottery_type is None:
            raise ValueError("无法自动检测彩票类型，请手动指定")
        logger.info(f"自动检测到彩票类型: {lottery_type}")

    return reader.read_csv(file_path, lottery_type)
