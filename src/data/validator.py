#!/usr/bin/env python3
"""
数据验证模块
提供彩票数据的验证功能
"""

import re
from datetime import datetime
from typing import List, Dict, Tuple, Optional
from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        """初始化验证器"""
        # 彩票类型规则
        self.rules = {
            LOTTERY_TYPE_DLT: {
                'front_count': 5,
                'back_count': 2,
                'front_range': (1, 35),
                'back_range': (1, 12),
                'total_numbers': 7
            },
            LOTTERY_TYPE_SSQ: {
                'front_count': 6,
                'back_count': 1,
                'front_range': (1, 33),
                'back_range': (1, 16),
                'total_numbers': 7
            },
            LOTTERY_TYPE_PL5: {
                'front_count': 5,
                'back_count': 0,
                'front_range': (0, 9),
                'back_range': None,
                'total_numbers': 5
            }
        }
    
    def validate_lottery_data(self, data: LotteryData) -> <PERSON><PERSON>[bool, List[str]]:
        """
        验证单条彩票数据
        
        Args:
            data: 彩票数据
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 验证彩票类型
        if data.lottery_type not in self.rules:
            errors.append(f"不支持的彩票类型: {data.lottery_type}")
            return False, errors
        
        rule = self.rules[data.lottery_type]
        
        # 验证期号格式
        if not self.validate_issue_number(data.issue_number):
            errors.append(f"期号格式错误: {data.issue_number}")
        
        # 验证开奖日期
        if not self.validate_draw_date(data.draw_date):
            errors.append(f"开奖日期无效: {data.draw_date}")
        
        # 验证号码数量
        if len(data.numbers) != rule['total_numbers']:
            errors.append(f"号码数量错误: 期望{rule['total_numbers']}个，实际{len(data.numbers)}个")
        
        # 验证号码范围和重复
        if not self.validate_numbers(data.numbers, data.lottery_type):
            errors.append("号码范围或重复性验证失败")
        
        return len(errors) == 0, errors
    
    def validate_issue_number(self, issue_number: str) -> bool:
        """验证期号格式"""
        if not issue_number:
            return False

        # 基本格式检查：支持4-6位数字格式
        # 4位格式：2007-2009年 (如"7001")
        # 5位格式：2010年以后 (如"24001")
        # 6位格式：预留未来使用
        pattern = r'^\d{4,6}$'
        return bool(re.match(pattern, issue_number))
    
    def validate_draw_date(self, draw_date: datetime) -> bool:
        """验证开奖日期"""
        if not isinstance(draw_date, datetime):
            return False
        
        # 检查日期是否在合理范围内
        min_date = datetime(2000, 1, 1)
        max_date = datetime.now()
        
        return min_date <= draw_date <= max_date
    
    def validate_numbers(self, numbers: List[int], lottery_type: str) -> bool:
        """验证号码"""
        if lottery_type not in self.rules:
            return False
        
        rule = self.rules[lottery_type]
        
        if lottery_type == LOTTERY_TYPE_PL5:
            # 排列五：允许重复，每个位置0-9
            return all(rule['front_range'][0] <= num <= rule['front_range'][1] for num in numbers)
        
        else:
            # 大乐透和双色球：前区不能重复，后区不能重复
            front_count = rule['front_count']
            back_count = rule['back_count']
            
            front_numbers = numbers[:front_count]
            back_numbers = numbers[front_count:] if back_count > 0 else []
            
            # 验证前区
            if not self.validate_number_range(front_numbers, rule['front_range']):
                return False
            
            if len(set(front_numbers)) != len(front_numbers):
                return False  # 前区有重复
            
            # 验证后区
            if back_count > 0:
                if not self.validate_number_range(back_numbers, rule['back_range']):
                    return False
                
                if len(set(back_numbers)) != len(back_numbers):
                    return False  # 后区有重复
        
        return True
    
    def validate_number_range(self, numbers: List[int], range_tuple: Tuple[int, int]) -> bool:
        """验证号码范围"""
        min_val, max_val = range_tuple
        return all(min_val <= num <= max_val for num in numbers)
    
    def validate_data_list(self, data_list: List[LotteryData]) -> Dict[str, any]:
        """
        验证数据列表
        
        Args:
            data_list: 数据列表
            
        Returns:
            验证结果字典
        """
        result = {
            'total_count': len(data_list),
            'valid_count': 0,
            'invalid_count': 0,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        if not data_list:
            result['errors'].append("数据列表为空")
            return result
        
        # 逐条验证
        for i, data in enumerate(data_list):
            is_valid, errors = self.validate_lottery_data(data)
            
            if is_valid:
                result['valid_count'] += 1
            else:
                result['invalid_count'] += 1
                for error in errors:
                    result['errors'].append(f"第{i+1}行: {error}")
        
        # 检查数据一致性
        self.check_data_consistency(data_list, result)
        
        # 生成统计信息
        self.generate_statistics(data_list, result)
        
        return result
    
    def check_data_consistency(self, data_list: List[LotteryData], result: Dict):
        """检查数据一致性"""
        if not data_list:
            return
        
        # 检查彩票类型一致性
        lottery_types = set(data.lottery_type for data in data_list)
        if len(lottery_types) > 1:
            result['warnings'].append(f"数据包含多种彩票类型: {lottery_types}")
        
        # 检查期号连续性
        issue_numbers = [data.issue_number for data in data_list]
        if len(set(issue_numbers)) != len(issue_numbers):
            result['warnings'].append("存在重复的期号")
        
        # 检查日期顺序
        dates = [data.draw_date for data in data_list]
        if dates != sorted(dates):
            result['warnings'].append("开奖日期不是按时间顺序排列")
    
    def generate_statistics(self, data_list: List[LotteryData], result: Dict):
        """生成统计信息"""
        if not data_list:
            return
        
        stats = {}
        
        # 日期范围
        dates = [data.draw_date for data in data_list]
        stats['date_range'] = {
            'start': min(dates),
            'end': max(dates),
            'span_days': (max(dates) - min(dates)).days
        }
        
        # 彩票类型分布
        lottery_types = {}
        for data in data_list:
            lottery_types[data.lottery_type] = lottery_types.get(data.lottery_type, 0) + 1
        stats['lottery_types'] = lottery_types
        
        # 期号范围
        issue_numbers = [data.issue_number for data in data_list]
        stats['issue_range'] = {
            'start': min(issue_numbers),
            'end': max(issue_numbers)
        }
        
        result['statistics'] = stats
    
    def get_validation_summary(self, validation_result: Dict) -> str:
        """获取验证结果摘要"""
        result = validation_result
        
        summary = []
        summary.append("数据验证结果:")
        summary.append(f"  总数据量: {result['total_count']}")
        summary.append(f"  有效数据: {result['valid_count']}")
        summary.append(f"  无效数据: {result['invalid_count']}")
        
        if result['invalid_count'] > 0:
            summary.append(f"  有效率: {result['valid_count']/result['total_count']:.1%}")
        
        if result['errors']:
            summary.append(f"\n错误信息 ({len(result['errors'])}条):")
            for error in result['errors'][:5]:  # 只显示前5条错误
                summary.append(f"  - {error}")
            if len(result['errors']) > 5:
                summary.append(f"  ... 还有{len(result['errors'])-5}条错误")
        
        if result['warnings']:
            summary.append(f"\n警告信息 ({len(result['warnings'])}条):")
            for warning in result['warnings']:
                summary.append(f"  - {warning}")
        
        if result['statistics']:
            stats = result['statistics']
            summary.append("\n数据统计:")
            
            if 'date_range' in stats:
                date_range = stats['date_range']
                summary.append(f"  日期范围: {date_range['start'].strftime('%Y-%m-%d')} 至 {date_range['end'].strftime('%Y-%m-%d')}")
                summary.append(f"  时间跨度: {date_range['span_days']} 天")
            
            if 'lottery_types' in stats:
                summary.append("  彩票类型分布:")
                for lottery_type, count in stats['lottery_types'].items():
                    summary.append(f"    {lottery_type}: {count} 期")
        
        return "\n".join(summary)


if __name__ == "__main__":
    # 测试数据验证器
    validator = DataValidator()
    
    # 创建测试数据
    test_data = [
        LotteryData("24001", datetime(2024, 1, 1), [1, 5, 15, 25, 35, 2, 8], LOTTERY_TYPE_DLT),
        LotteryData("24002", datetime(2024, 1, 3), [3, 7, 17, 27, 33, 1, 9], LOTTERY_TYPE_DLT),
        LotteryData("invalid", datetime(2024, 1, 5), [1, 2, 3], LOTTERY_TYPE_DLT),  # 无效数据
    ]
    
    # 验证数据
    result = validator.validate_data_list(test_data)
    print(validator.get_validation_summary(result))
