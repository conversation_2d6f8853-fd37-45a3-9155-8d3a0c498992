"""
数据处理模块

包含彩票数据模型定义、数据读取器和配置文件。
负责处理不同类型彩票数据的读取、解析和统一格式转换。
"""

from .models import LotteryData, create_lottery_data
from .config import (
    LOTTERY_RULES,
    LOTTERY_TYPE_DLT,
    LOTTERY_TYPE_SSQ,
    LOTTERY_TYPE_PL5,
    get_lottery_rule,
    get_number_range,
    get_big_small_boundary,
    get_zones,
    validate_numbers
)
from .reader import DataReader, read_lottery_data

__all__ = [
    'LotteryData',
    'create_lottery_data',
    'LOTTERY_RULES',
    'LOTTERY_TYPE_DLT',
    'LOTTERY_TYPE_SSQ',
    'LOTTERY_TYPE_PL5',
    'get_lottery_rule',
    'get_number_range',
    'get_big_small_boundary',
    'get_zones',
    'validate_numbers',
    'DataReader',
    'read_lottery_data'
]
