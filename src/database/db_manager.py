"""
数据库管理器

提供SQLite数据库的连接、操作和管理功能。
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path
import json

from .models import LotteryRecord, CREATE_TABLE_SQL, CREATE_INDEXES_SQL

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    数据库管理器类
    
    负责SQLite数据库的连接、表创建、数据操作等功能。
    """
    
    def __init__(self, db_path: str = "lottery_data.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path (str): 数据库文件路径
        """
        self.db_path = db_path
        self.connection = None
        self._ensure_database_exists()
    
    def _ensure_database_exists(self):
        """确保数据库文件和表结构存在"""
        try:
            self.connect()
            self.create_tables()
            logger.info(f"数据库初始化完成: {self.db_path}")
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def connect(self):
        """连接数据库"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # 使结果可以按列名访问
            logger.debug(f"数据库连接成功: {self.db_path}")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
            logger.debug("数据库连接已断开")
    
    def create_tables(self):
        """创建数据库表和索引"""
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor()
            
            # 创建主表
            cursor.execute(CREATE_TABLE_SQL)
            
            # 创建索引
            for index_sql in CREATE_INDEXES_SQL:
                cursor.execute(index_sql)
            
            self.connection.commit()
            logger.info("数据库表和索引创建完成")
            
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            raise
    
    def insert_record(self, record: LotteryRecord) -> int:
        """
        插入单条记录
        
        Args:
            record (LotteryRecord): 彩票记录
            
        Returns:
            int: 插入记录的ID
        """
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor()
            
            sql = """
            INSERT OR REPLACE INTO lottery_data (
                lottery_type, issue_number, draw_date, front_numbers, back_numbers,
                odd_even_ratio, odd_even_pattern, big_small_ratio, big_small_pattern,
                last_odd_even_pattern_date, last_odd_even_pattern_interval,
                last_big_small_pattern_date, last_big_small_pattern_interval
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            values = (
                record.lottery_type,
                record.issue_number,
                record.draw_date.isoformat() if record.draw_date else None,
                json.dumps(record.front_numbers),
                json.dumps(record.back_numbers),
                record.odd_even_ratio,
                record.odd_even_pattern,
                record.big_small_ratio,
                record.big_small_pattern,
                record.last_odd_even_pattern_date.isoformat() if record.last_odd_even_pattern_date else None,
                record.last_odd_even_pattern_interval,
                record.last_big_small_pattern_date.isoformat() if record.last_big_small_pattern_date else None,
                record.last_big_small_pattern_interval
            )
            
            cursor.execute(sql, values)
            self.connection.commit()
            
            record_id = cursor.lastrowid
            logger.debug(f"插入记录成功: {record.lottery_type} {record.issue_number}")
            return record_id
            
        except Exception as e:
            logger.error(f"插入记录失败: {e}")
            raise
    
    def insert_records(self, records: List[LotteryRecord]) -> int:
        """
        批量插入记录
        
        Args:
            records (List[LotteryRecord]): 彩票记录列表
            
        Returns:
            int: 插入的记录数量
        """
        if not records:
            return 0
        
        count = 0
        for record in records:
            try:
                self.insert_record(record)
                count += 1
            except Exception as e:
                logger.warning(f"插入记录失败，跳过: {record.lottery_type} {record.issue_number}, 错误: {e}")
        
        logger.info(f"批量插入完成，成功插入 {count}/{len(records)} 条记录")
        return count
    
    def get_records(self, lottery_type: str = None, limit: int = None, 
                   order_by: str = "draw_date DESC") -> List[LotteryRecord]:
        """
        查询记录
        
        Args:
            lottery_type (str, optional): 彩票类型过滤
            limit (int, optional): 限制返回数量
            order_by (str): 排序方式
            
        Returns:
            List[LotteryRecord]: 彩票记录列表
        """
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor()
            
            sql = "SELECT * FROM lottery_data"
            params = []
            
            if lottery_type:
                sql += " WHERE lottery_type = ?"
                params.append(lottery_type)
            
            sql += f" ORDER BY {order_by}"
            
            if limit:
                sql += " LIMIT ?"
                params.append(limit)
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            
            records = []
            for row in rows:
                record_dict = dict(row)
                # 转换日期字符串为datetime对象
                if record_dict['draw_date']:
                    record_dict['draw_date'] = datetime.fromisoformat(record_dict['draw_date'])
                if record_dict['last_odd_even_pattern_date']:
                    record_dict['last_odd_even_pattern_date'] = datetime.fromisoformat(record_dict['last_odd_even_pattern_date'])
                if record_dict['last_big_small_pattern_date']:
                    record_dict['last_big_small_pattern_date'] = datetime.fromisoformat(record_dict['last_big_small_pattern_date'])
                
                records.append(LotteryRecord.from_dict(record_dict))
            
            logger.debug(f"查询到 {len(records)} 条记录")
            return records
            
        except Exception as e:
            logger.error(f"查询记录失败: {e}")
            raise
    
    def get_record_by_issue(self, lottery_type: str, issue_number: str) -> Optional[LotteryRecord]:
        """
        根据期号查询单条记录
        
        Args:
            lottery_type (str): 彩票类型
            issue_number (str): 期号
            
        Returns:
            Optional[LotteryRecord]: 彩票记录或None
        """
        records = self.get_records(lottery_type)
        for record in records:
            if record.issue_number == issue_number:
                return record
        return None
    
    def update_last_patterns(self, record_id: int, 
                           last_odd_even_date: datetime = None, last_odd_even_interval: int = None,
                           last_big_small_date: datetime = None, last_big_small_interval: int = None):
        """
        更新记录的上次模式信息
        
        Args:
            record_id (int): 记录ID
            last_odd_even_date (datetime, optional): 上次奇偶模式日期
            last_odd_even_interval (int, optional): 上次奇偶模式间隔
            last_big_small_date (datetime, optional): 上次大小模式日期
            last_big_small_interval (int, optional): 上次大小模式间隔
        """
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor()
            
            updates = []
            params = []
            
            if last_odd_even_date is not None:
                updates.append("last_odd_even_pattern_date = ?")
                params.append(last_odd_even_date.isoformat())
            
            if last_odd_even_interval is not None:
                updates.append("last_odd_even_pattern_interval = ?")
                params.append(last_odd_even_interval)
            
            if last_big_small_date is not None:
                updates.append("last_big_small_pattern_date = ?")
                params.append(last_big_small_date.isoformat())
            
            if last_big_small_interval is not None:
                updates.append("last_big_small_pattern_interval = ?")
                params.append(last_big_small_interval)
            
            if not updates:
                return
            
            sql = f"UPDATE lottery_data SET {', '.join(updates)} WHERE id = ?"
            params.append(record_id)
            
            cursor.execute(sql, params)
            self.connection.commit()
            
            logger.debug(f"更新记录 {record_id} 的上次模式信息")
            
        except Exception as e:
            logger.error(f"更新上次模式信息失败: {e}")
            raise
    
    def get_count(self, lottery_type: str = None) -> int:
        """
        获取记录总数
        
        Args:
            lottery_type (str, optional): 彩票类型过滤
            
        Returns:
            int: 记录总数
        """
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor()
            
            if lottery_type:
                cursor.execute("SELECT COUNT(*) FROM lottery_data WHERE lottery_type = ?", (lottery_type,))
            else:
                cursor.execute("SELECT COUNT(*) FROM lottery_data")
            
            return cursor.fetchone()[0]
            
        except Exception as e:
            logger.error(f"获取记录总数失败: {e}")
            raise
    
    def clear_all_data(self):
        """清空所有数据"""
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor()
            cursor.execute("DELETE FROM lottery_data")
            self.connection.commit()
            logger.info("所有数据已清空")
            
        except Exception as e:
            logger.error(f"清空数据失败: {e}")
            raise
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
