"""
数据迁移脚本

从现有数据文件迁移数据到SQLite数据库。
"""

import logging
from typing import List
from datetime import datetime
import os
import sys

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.data.reader import read_lottery_data
from src.database.db_manager import DatabaseManager
from src.database.models import LotteryRecord
from src.calculator.basic_calculator import BasicCalculator
from src.calculator.pattern_calculator import PatternCalculator

logger = logging.getLogger(__name__)


class DataMigration:
    """
    数据迁移类
    
    负责从现有数据文件迁移数据到数据库。
    """
    
    def __init__(self, db_path: str = "lottery_data.db"):
        """
        初始化数据迁移器
        
        Args:
            db_path (str): 数据库文件路径
        """
        self.db_manager = DatabaseManager(db_path)
    
    def convert_lottery_data_to_record(self, lottery_data, lottery_type: str) -> LotteryRecord:
        """
        将LotteryData对象转换为LotteryRecord
        
        Args:
            lottery_data: 原始彩票数据对象
            lottery_type (str): 彩票类型
            
        Returns:
            LotteryRecord: 数据库记录对象
        """
        # 创建基础记录
        record = LotteryRecord()
        record.lottery_type = lottery_type
        record.issue_number = lottery_data.issue_number
        record.draw_date = lottery_data.draw_date
        record.front_numbers = lottery_data.get_front_numbers()
        record.back_numbers = lottery_data.get_back_numbers()
        
        # 计算基础属性
        attributes = BasicCalculator.calculate_all_attributes(
            record.front_numbers, record.back_numbers, lottery_type
        )
        
        record.odd_even_ratio = attributes['odd_even_ratio']
        record.odd_even_pattern = attributes['odd_even_pattern']
        record.big_small_ratio = attributes['big_small_ratio']
        record.big_small_pattern = attributes['big_small_pattern']
        
        return record
    
    def migrate_from_file(self, file_path: str, lottery_type: str) -> int:
        """
        从文件迁移数据
        
        Args:
            file_path (str): 数据文件路径
            lottery_type (str): 彩票类型
            
        Returns:
            int: 迁移的记录数量
        """
        logger.info(f"开始从文件迁移数据: {file_path}")
        
        try:
            # 读取原始数据
            lottery_data_list = read_lottery_data(file_path, lottery_type)
            logger.info(f"读取到 {len(lottery_data_list)} 条原始数据")
            
            if not lottery_data_list:
                logger.warning("没有读取到数据")
                return 0
            
            # 转换为数据库记录
            records = []
            for lottery_data in lottery_data_list:
                try:
                    record = self.convert_lottery_data_to_record(lottery_data, lottery_type)
                    records.append(record)
                except Exception as e:
                    logger.warning(f"转换记录失败，跳过: {lottery_data.issue_number}, 错误: {e}")
            
            logger.info(f"成功转换 {len(records)} 条记录")
            
            # 按时间排序（用于计算上次模式）
            records.sort(key=lambda x: x.draw_date)
            
            # 计算上次模式信息
            logger.info("开始计算上次模式信息...")
            PatternCalculator.batch_update_last_patterns(records)
            
            # 插入数据库
            logger.info("开始插入数据库...")
            count = self.db_manager.insert_records(records)
            
            logger.info(f"数据迁移完成，成功迁移 {count} 条记录")
            return count
            
        except Exception as e:
            logger.error(f"数据迁移失败: {e}")
            raise
    
    def migrate_all_data(self, data_dir: str = "data") -> dict:
        """
        迁移所有数据文件
        
        Args:
            data_dir (str): 数据目录路径
            
        Returns:
            dict: 迁移结果统计
        """
        results = {}
        
        # 定义数据文件映射
        file_mappings = {
            'dlt': ['dlt.txt', 'dlt_data.txt', '大乐透.txt'],
            'ssq': ['ssq.txt', 'ssq_data.txt', '双色球.txt']
        }
        
        for lottery_type, file_names in file_mappings.items():
            results[lottery_type] = 0
            
            for file_name in file_names:
                file_path = os.path.join(data_dir, file_name)
                
                if os.path.exists(file_path):
                    try:
                        count = self.migrate_from_file(file_path, lottery_type)
                        results[lottery_type] += count
                        logger.info(f"{lottery_type} 从 {file_name} 迁移了 {count} 条记录")
                        break  # 找到第一个存在的文件就停止
                    except Exception as e:
                        logger.error(f"迁移文件 {file_path} 失败: {e}")
                        continue
        
        return results
    
    def verify_migration(self) -> bool:
        """
        验证迁移结果
        
        Returns:
            bool: 验证是否通过
        """
        logger.info("开始验证迁移结果...")
        
        try:
            # 检查数据库中的记录数量
            dlt_count = self.db_manager.get_count('dlt')
            ssq_count = self.db_manager.get_count('ssq')
            total_count = self.db_manager.get_count()
            
            logger.info(f"数据库记录统计: 大乐透 {dlt_count} 条, 双色球 {ssq_count} 条, 总计 {total_count} 条")
            
            # 检查一些记录的计算结果
            records = self.db_manager.get_records(limit=10)
            
            for record in records:
                if not PatternCalculator.validate_pattern_calculation(record):
                    logger.error(f"记录 {record.lottery_type} {record.issue_number} 计算结果验证失败")
                    return False
            
            logger.info("迁移结果验证通过")
            return True
            
        except Exception as e:
            logger.error(f"验证迁移结果失败: {e}")
            return False
    
    def clear_and_migrate(self, data_dir: str = "data") -> dict:
        """
        清空数据库并重新迁移
        
        Args:
            data_dir (str): 数据目录路径
            
        Returns:
            dict: 迁移结果统计
        """
        logger.info("清空数据库并重新迁移...")
        
        # 清空现有数据
        self.db_manager.clear_all_data()
        
        # 重新迁移
        results = self.migrate_all_data(data_dir)
        
        # 验证结果
        if self.verify_migration():
            logger.info("数据迁移和验证完成")
        else:
            logger.error("数据迁移验证失败")
        
        return results


def main():
    """主函数 - 执行数据迁移"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        migration = DataMigration()
        results = migration.clear_and_migrate()
        
        print("\n=== 数据迁移完成 ===")
        for lottery_type, count in results.items():
            print(f"{lottery_type.upper()}: {count} 条记录")
        
        print(f"总计: {sum(results.values())} 条记录")
        
    except Exception as e:
        logger.error(f"数据迁移失败: {e}")
        print(f"数据迁移失败: {e}")


if __name__ == "__main__":
    main()
