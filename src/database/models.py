"""
数据库模型定义

定义彩票数据的数据库表结构和数据模型。
"""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional
import json


@dataclass
class LotteryRecord:
    """
    彩票记录数据模型
    
    对应数据库中的lottery_data表
    """
    
    # 基础信息
    id: Optional[int] = None
    lottery_type: str = ""  # 'dlt', 'ssq'
    issue_number: str = ""
    draw_date: datetime = None
    front_numbers: List[int] = None  # 前区/红球号码
    back_numbers: List[int] = None   # 后区/蓝球号码
    
    # 基础计算属性
    odd_even_ratio: str = ""         # "3:2"
    odd_even_pattern: str = ""       # "10110"
    big_small_ratio: str = ""        # "2:3"
    big_small_pattern: str = ""      # "01101"
    zone_distribution: str = ""      # "2:1:0:0:1:1:0"
    
    # 上次模式属性
    last_odd_even_pattern_date: Optional[datetime] = None
    last_odd_even_pattern_interval: Optional[int] = None
    last_big_small_pattern_date: Optional[datetime] = None
    last_big_small_pattern_interval: Optional[int] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.front_numbers is None:
            self.front_numbers = []
        if self.back_numbers is None:
            self.back_numbers = []
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'id': self.id,
            'lottery_type': self.lottery_type,
            'issue_number': self.issue_number,
            'draw_date': self.draw_date.isoformat() if self.draw_date else None,
            'front_numbers': json.dumps(self.front_numbers),
            'back_numbers': json.dumps(self.back_numbers),
            'odd_even_ratio': self.odd_even_ratio,
            'odd_even_pattern': self.odd_even_pattern,
            'big_small_ratio': self.big_small_ratio,
            'big_small_pattern': self.big_small_pattern,
            'last_odd_even_pattern_date': self.last_odd_even_pattern_date.isoformat() if self.last_odd_even_pattern_date else None,
            'last_odd_even_pattern_interval': self.last_odd_even_pattern_interval,
            'last_big_small_pattern_date': self.last_big_small_pattern_date.isoformat() if self.last_big_small_pattern_date else None,
            'last_big_small_pattern_interval': self.last_big_small_pattern_interval
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'LotteryRecord':
        """从字典创建对象"""
        record = cls()
        record.id = data.get('id')
        record.lottery_type = data.get('lottery_type', '')
        record.issue_number = data.get('issue_number', '')
        
        # 处理日期
        if data.get('draw_date'):
            if isinstance(data['draw_date'], str):
                record.draw_date = datetime.fromisoformat(data['draw_date'])
            else:
                record.draw_date = data['draw_date']
        
        # 处理号码数组
        if data.get('front_numbers'):
            if isinstance(data['front_numbers'], str):
                record.front_numbers = json.loads(data['front_numbers'])
            else:
                record.front_numbers = data['front_numbers']
        
        if data.get('back_numbers'):
            if isinstance(data['back_numbers'], str):
                record.back_numbers = json.loads(data['back_numbers'])
            else:
                record.back_numbers = data['back_numbers']
        
        # 基础属性
        record.odd_even_ratio = data.get('odd_even_ratio', '')
        record.odd_even_pattern = data.get('odd_even_pattern', '')
        record.big_small_ratio = data.get('big_small_ratio', '')
        record.big_small_pattern = data.get('big_small_pattern', '')
        
        # 上次模式属性
        if data.get('last_odd_even_pattern_date'):
            if isinstance(data['last_odd_even_pattern_date'], str):
                record.last_odd_even_pattern_date = datetime.fromisoformat(data['last_odd_even_pattern_date'])
            else:
                record.last_odd_even_pattern_date = data['last_odd_even_pattern_date']
        
        record.last_odd_even_pattern_interval = data.get('last_odd_even_pattern_interval')
        
        if data.get('last_big_small_pattern_date'):
            if isinstance(data['last_big_small_pattern_date'], str):
                record.last_big_small_pattern_date = datetime.fromisoformat(data['last_big_small_pattern_date'])
            else:
                record.last_big_small_pattern_date = data['last_big_small_pattern_date']
        
        record.last_big_small_pattern_interval = data.get('last_big_small_pattern_interval')
        
        return record
    
    def get_all_numbers(self) -> List[int]:
        """获取所有号码（前区+后区）"""
        return self.front_numbers + self.back_numbers
    
    def format_front_numbers(self) -> str:
        """格式化前区号码显示"""
        return " ".join(f"{num:02d}" for num in self.front_numbers)
    
    def format_back_numbers(self) -> str:
        """格式化后区号码显示"""
        return " ".join(f"{num:02d}" for num in self.back_numbers)
    
    def format_all_numbers(self) -> str:
        """格式化所有号码显示"""
        front = self.format_front_numbers()
        back = self.format_back_numbers()
        return f"{front} + {back}" if back else front
    
    def format_last_odd_even_pattern(self) -> str:
        """格式化上次奇偶排布显示"""
        if self.last_odd_even_pattern_date and self.last_odd_even_pattern_interval is not None:
            date_str = self.last_odd_even_pattern_date.strftime('%Y-%m-%d')
            return f"{date_str} ({self.last_odd_even_pattern_interval})"
        return ""
    
    def format_last_big_small_pattern(self) -> str:
        """格式化上次大小排布显示"""
        if self.last_big_small_pattern_date and self.last_big_small_pattern_interval is not None:
            date_str = self.last_big_small_pattern_date.strftime('%Y-%m-%d')
            return f"{date_str} ({self.last_big_small_pattern_interval})"
        return ""


# 数据库表创建SQL
CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS lottery_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    lottery_type TEXT NOT NULL,
    issue_number TEXT NOT NULL,
    draw_date DATE NOT NULL,
    front_numbers TEXT NOT NULL,
    back_numbers TEXT NOT NULL,
    
    odd_even_ratio TEXT DEFAULT '',
    odd_even_pattern TEXT DEFAULT '',
    big_small_ratio TEXT DEFAULT '',
    big_small_pattern TEXT DEFAULT '',
    
    last_odd_even_pattern_date DATE,
    last_odd_even_pattern_interval INTEGER,
    last_big_small_pattern_date DATE,
    last_big_small_pattern_interval INTEGER,
    
    UNIQUE(lottery_type, issue_number)
);
"""

# 创建索引SQL
CREATE_INDEXES_SQL = [
    "CREATE INDEX IF NOT EXISTS idx_lottery_type ON lottery_data(lottery_type);",
    "CREATE INDEX IF NOT EXISTS idx_draw_date ON lottery_data(draw_date);",
    "CREATE INDEX IF NOT EXISTS idx_issue_number ON lottery_data(issue_number);",
    "CREATE INDEX IF NOT EXISTS idx_odd_even_pattern ON lottery_data(odd_even_pattern);",
    "CREATE INDEX IF NOT EXISTS idx_big_small_pattern ON lottery_data(big_small_pattern);"
]
