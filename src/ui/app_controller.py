#!/usr/bin/env python3
"""
应用程序控制器
连接UI界面和业务逻辑
"""

import os
import sys
import csv
import threading
from datetime import datetime
from typing import Dict, List, Optional
from tkinter import messagebox, filedialog

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.ui.simple_main_window import SimpleMainWindow
from src.ui.widgets import DataPreviewDialog
from src.ui.dialogs import FileImportDialog, AnalysisOptionsDialog, ResultExportDialog
from src.data.reader import DataReader
from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5
from src.data.validator import DataValidator
from src.analysis.dlt_analyzer import DLTAnalyzer
from src.analysis.ssq_analyzer import SSQAnalyzer
from src.analysis.pl5_analyzer import PL5Analyzer


class AppController:
    """应用程序控制器"""
    
    def __init__(self):
        """初始化控制器"""
        self.main_window = SimpleMainWindow()
        self.data_reader = DataReader()
        self.data_validator = DataValidator()
        self.current_data: List[LotteryData] = []
        self.current_analyzer = None
        self.progress_dialog: Optional[ProgressDialog] = None

        # 绑定事件处理函数
        self.setup_event_handlers()
        
    def setup_event_handlers(self):
        """设置事件处理函数"""
        self.main_window.on_import_data = self.handle_import_data
        self.main_window.on_start_analysis = self.handle_start_analysis
        self.main_window.on_export_results = self.handle_export_results
        self.main_window.on_clear_results = self.handle_clear_results
    
    def handle_import_data(self):
        """处理导入数据"""
        try:
            # 获取彩票类型
            lottery_type = self.main_window.get_lottery_type()

            # 使用简单的文件选择对话框
            from tkinter import filedialog
            file_path = filedialog.askopenfilename(
                title="选择大乐透数据文件",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
                initialdir="data"
            )

            if not file_path:
                return

            # 直接在主线程中读取数据（移除进度对话框）
            try:
                # 读取数据
                raw_data = self.data_reader.read_csv(file_path, lottery_type)

                # 验证数据
                validation_result = self.data_validator.validate_data_list(raw_data)

                # 过滤有效数据
                valid_data = []
                for i, data in enumerate(raw_data):
                    is_valid, _ = self.data_validator.validate_lottery_data(data)
                    if is_valid:
                        valid_data.append(data)

                self.current_data = valid_data

                # 直接更新UI
                self.on_data_loaded(file_path, validation_result)

            except Exception as e:
                # 显示错误
                self.on_data_load_error(str(e))
            
        except Exception as e:
            self.main_window.show_message("错误", f"导入数据失败：{str(e)}", "error")
    
    def on_data_loaded(self, file_path: str, validation_result: Dict):
        """数据加载完成"""
        # 更新文件路径
        self.main_window.data_file_path.set(file_path)

        # 更新数据表格显示
        if hasattr(self.main_window, 'set_data_table'):
            self.main_window.set_data_table(self.current_data)

        # 自动执行所有分析（移除提示消息）
        if validation_result['valid_count'] > 0:
            self.auto_analyze_all_data()
    
    def on_data_load_error(self, error_message: str):
        """数据加载错误"""
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        self.main_window.show_message("错误", f"导入数据失败：{error_message}", "error")
    
    def preview_data(self):
        """预览数据"""
        if not self.current_data:
            return
        
        # 准备预览数据
        preview_data = []
        for i, data in enumerate(self.current_data[:10]):  # 只预览前10条
            row = [
                data.issue_number,
                data.draw_date.strftime("%Y-%m-%d"),
                ",".join(map(str, data.numbers)),
                data.lottery_type
            ]
            preview_data.append(row)
        
        # 显示预览对话框
        if preview_data:
            DataPreviewDialog(self.main_window.root, preview_data, "数据预览")
    
    def handle_start_analysis(self):
        """处理开始分析"""
        try:
            # 检查是否有数据
            if not self.current_data:
                self.main_window.show_message("提示", "请先导入数据", "warning")
                return

            # 获取当前分析选项
            current_options = self.main_window.get_analysis_options()

            # 显示分析选项配置对话框
            options_dialog = AnalysisOptionsDialog(self.main_window.root, current_options)
            self.main_window.root.wait_window(options_dialog.dialog)

            options = options_dialog.result
            if not options:
                return  # 用户取消了

            # 更新主窗口的选项
            for key, value in options.items():
                if key in self.main_window.analysis_options:
                    self.main_window.analysis_options[key].set(value)
            
            # 获取彩票类型
            lottery_type = self.main_window.get_lottery_type()
            
            # 创建分析器
            self.current_analyzer = self.create_analyzer(lottery_type)
            
            # 显示进度对话框
            self.progress_dialog = ProgressDialog(
                self.main_window.root,
                "分析中",
                "正在进行数据分析..."
            )
            
            # 在后台线程中进行分析
            def analyze_data():
                try:
                    # 加载数据到分析器
                    self.current_analyzer.load_data(self.current_data)
                    
                    # 执行分析
                    results = self.perform_analysis(options)
                    
                    # 在主线程中更新结果
                    self.main_window.root.after(0, self.on_analysis_complete, results)
                    
                except Exception as e:
                    # 在主线程中显示错误
                    self.main_window.root.after(0, self.on_analysis_error, str(e))
            
            # 启动后台线程
            thread = threading.Thread(target=analyze_data)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            self.main_window.show_message("错误", f"分析失败：{str(e)}", "error")
    
    def create_analyzer(self, lottery_type: str):
        """创建分析器"""
        if lottery_type == LOTTERY_TYPE_DLT:
            return DLTAnalyzer()
        elif lottery_type == LOTTERY_TYPE_SSQ:
            return SSQAnalyzer()
        elif lottery_type == LOTTERY_TYPE_PL5:
            return PL5Analyzer()
        else:
            raise ValueError(f"不支持的彩票类型: {lottery_type}")

    def get_lottery_display_name(self) -> str:
        """获取彩票类型显示名称"""
        lottery_type = self.main_window.get_lottery_type()
        names = {
            LOTTERY_TYPE_DLT: '大乐透',
            LOTTERY_TYPE_SSQ: '双色球',
            LOTTERY_TYPE_PL5: '排列五'
        }
        return names.get(lottery_type, '未知类型')
    
    def perform_analysis(self, options: Dict[str, bool]) -> str:
        """执行分析"""
        results = []
        results.append("=" * 80)
        results.append("彩票号码分析系统 - 分析结果报告")
        results.append("=" * 80)
        results.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        results.append(f"彩票类型: {self.get_lottery_display_name()}")
        results.append(f"数据期数: {len(self.current_data)} 期")
        results.append(f"数据范围: {self.current_data[0].draw_date.strftime('%Y-%m-%d')} 至 {self.current_data[-1].draw_date.strftime('%Y-%m-%d')}")
        results.append("")
        
        # 奇偶分析
        if options.get('odd_even', False):
            results.append("【奇偶分析】")
            results.append("-" * 60)
            try:
                # 前区分析
                front_result = self.current_analyzer.analyze_odd_even_distribution('front')
                results.append(f"前区奇偶分析：")
                results.append(f"  总期数：{front_result['total_periods']}")
                results.append(f"  最新模式：{front_result['latest_pattern']}")

                # 显示模式统计
                if 'pattern_statistics' in front_result:
                    results.append("  模式统计（前5位）：")
                    pattern_stats = front_result['pattern_statistics']
                    sorted_patterns = sorted(pattern_stats.items(), key=lambda x: x[1]['count'], reverse=True)
                    for i, (pattern, stats) in enumerate(sorted_patterns[:5]):
                        results.append(f"    {pattern}: {stats['count']}次 ({stats['frequency']:.1%})")

                # 后区分析（如果支持）
                if hasattr(self.current_analyzer, 'rules') and 'back_count' in self.current_analyzer.rules:
                    back_result = self.current_analyzer.analyze_odd_even_distribution('back')
                    results.append(f"后区奇偶分析：")
                    results.append(f"  总期数：{back_result['total_periods']}")
                    results.append(f"  最新模式：{back_result['latest_pattern']}")

            except Exception as e:
                results.append(f"  分析失败：{str(e)}")
            results.append("")
        
        # 大小分析
        if options.get('big_small', False):
            results.append("【大小分析】")
            results.append("-" * 60)
            try:
                front_result = self.current_analyzer.analyze_big_small_distribution('front')
                results.append(f"前区大小分析：")
                results.append(f"  总期数：{front_result['total_periods']}")
                results.append(f"  最新模式：{front_result['latest_pattern']}")

                # 显示模式统计
                if 'pattern_statistics' in front_result:
                    results.append("  模式统计（前5位）：")
                    pattern_stats = front_result['pattern_statistics']
                    sorted_patterns = sorted(pattern_stats.items(), key=lambda x: x[1]['count'], reverse=True)
                    for i, (pattern, stats) in enumerate(sorted_patterns[:5]):
                        results.append(f"    {pattern}: {stats['count']}次 ({stats['frequency']:.1%})")

                # 后区分析（如果支持）
                if hasattr(self.current_analyzer, 'rules') and 'back_count' in self.current_analyzer.rules:
                    back_result = self.current_analyzer.analyze_big_small_distribution('back')
                    results.append(f"后区大小分析：")
                    results.append(f"  总期数：{back_result['total_periods']}")
                    results.append(f"  最新模式：{back_result['latest_pattern']}")

            except Exception as e:
                results.append(f"  分析失败：{str(e)}")
            results.append("")
        
        # 号码遗漏分析
        if options.get('missing', False):
            results.append("【号码遗漏分析】")
            results.append("-" * 60)
            try:
                missing_result = self.current_analyzer.calculate_all_numbers_missing('front')
                results.append(f"前区遗漏分析：")
                results.append(f"  总号码数：{missing_result['total_numbers']}")
                results.append(f"  总期数：{missing_result['total_periods']}")

                # 显示最大遗漏号码
                if missing_result.get('most_missing'):
                    most_missing = missing_result['most_missing']
                    results.append(f"  最大遗漏：{most_missing['number']} (遗漏{most_missing['missing']}期)")

                # 显示遗漏统计
                if 'missing_details' in missing_result:
                    results.append("  遗漏期数统计（前10位）：")
                    missing_details = missing_result['missing_details']
                    sorted_missing = sorted(missing_details.items(), key=lambda x: x[1], reverse=True)
                    for i, (number, missing) in enumerate(sorted_missing[:10]):
                        results.append(f"    号码{number}: 遗漏{missing}期")

            except Exception as e:
                results.append(f"  分析失败：{str(e)}")
            results.append("")
        
        # 分区比分析
        if options.get('zone_ratio', False):
            results.append("【分区比分析】")
            results.append("-" * 60)
            try:
                zone_result = self.current_analyzer.analyze_zone_ratio('front')
                results.append(f"前区分区比分析：")
                results.append(f"  总期数：{zone_result['total_periods']}")
                results.append(f"  最新模式：{zone_result['latest_pattern']}")

                # 显示分区比统计
                if 'pattern_statistics' in zone_result:
                    results.append("  分区比统计（前5位）：")
                    pattern_stats = zone_result['pattern_statistics']
                    sorted_patterns = sorted(pattern_stats.items(), key=lambda x: x[1]['count'], reverse=True)
                    for i, (pattern, stats) in enumerate(sorted_patterns[:5]):
                        results.append(f"    {pattern}: {stats['count']}次 ({stats['frequency']:.1%})")

            except Exception as e:
                results.append(f"  分析失败：{str(e)}")
            results.append("")

        results.append("=" * 80)
        results.append("分析完成")
        results.append(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        results.append("=" * 80)
        
        return "\n".join(results)
    
    def on_analysis_complete(self, results: str):
        """分析完成"""
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        # 显示结果
        self.main_window.set_results_text(results)
        
        # 显示成功消息
        self.main_window.show_message("成功", "分析完成", "info")
    
    def on_analysis_error(self, error_message: str):
        """分析错误"""
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        self.main_window.show_message("错误", f"分析失败：{error_message}", "error")
    
    def handle_export_results(self):
        """处理导出结果"""
        try:
            # 获取结果文本
            results_text = self.main_window.results_text.get(1.0, "end-1c")

            if not results_text.strip():
                self.main_window.show_message("提示", "没有可导出的结果", "warning")
                return

            # 使用专用的导出对话框
            export_dialog = ResultExportDialog(self.main_window.root, results_text)
            self.main_window.root.wait_window(export_dialog.dialog)

            if export_dialog.result:
                self.main_window.show_message("成功", f"结果已导出到：{export_dialog.result}", "info")

        except Exception as e:
            self.main_window.show_message("错误", f"导出失败：{str(e)}", "error")
    
    def handle_clear_results(self):
        """处理清除结果"""
        # 清除分析结果
        self.main_window.clear_results()
        # 清除当前数据
        self.current_data = []
        # 清除文件路径
        self.main_window.data_file_path.set("")
    
    def auto_analyze_all_data(self):
        """自动执行所有分析"""
        try:
            # 获取彩票类型
            lottery_type = self.main_window.get_lottery_type()

            # 创建分析器
            self.current_analyzer = self.create_analyzer(lottery_type)

            # 直接在主线程中进行分析（移除进度对话框）
            try:
                # 加载数据到分析器
                self.current_analyzer.load_data(self.current_data)

                # 执行所有分析（启用所有选项）
                all_options = {
                    'odd_even': True,
                    'big_small': True,
                    'missing': True,
                    'zone_ratio': True
                }

                results = self.perform_analysis(all_options)

                # 直接更新结果
                self.on_auto_analysis_complete(results)

            except Exception as e:
                # 显示错误
                self.on_analysis_error(str(e))

        except Exception as e:
            self.main_window.show_message("错误", f"自动分析失败：{str(e)}", "error")

    def on_auto_analysis_complete(self, results: str):
        """自动分析完成"""
        # 显示分析结果（移除提示消息）
        self.main_window.set_results_text(results)

    def run(self):
        """运行应用程序"""
        self.main_window.run()


if __name__ == "__main__":
    # 启动应用程序
    app = AppController()
    app.run()
