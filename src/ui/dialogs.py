#!/usr/bin/env python3
"""
对话框模块
提供各种专用对话框
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import csv
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any


class FileImportDialog:
    """文件导入对话框"""
    
    def __init__(self, parent, lottery_type: str):
        """初始化文件导入对话框"""
        self.parent = parent
        self.lottery_type = lottery_type
        self.result = None
        self.file_path = ""
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("导入数据文件")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        
        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建控件
        self.create_widgets()
        
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
    
    def create_widgets(self):
        """创建控件"""
        # 标题
        title_label = ttk.Label(
            self.dialog,
            text=f"导入{self.get_lottery_name()}数据文件",
            font=('Arial', 12, 'bold')
        )
        title_label.pack(pady=10)
        
        # 文件选择框架
        file_frame = ttk.LabelFrame(self.dialog, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 文件路径
        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill=tk.X)
        
        ttk.Label(path_frame, text="文件路径:").pack(side=tk.LEFT)
        
        self.path_var = tk.StringVar()
        self.path_entry = ttk.Entry(path_frame, textvariable=self.path_var, state='readonly')
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        
        ttk.Button(path_frame, text="浏览", command=self.browse_file).pack(side=tk.RIGHT)
        
        # 文件信息
        self.info_label = ttk.Label(file_frame, text="请选择CSV格式的数据文件")
        self.info_label.pack(pady=(10, 0))
        
        # 数据预览框架
        preview_frame = ttk.LabelFrame(self.dialog, text="数据预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建表格
        self.create_preview_table(preview_frame)
        
        # 按钮框架
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="确定", command=self.confirm).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT)
    
    def create_preview_table(self, parent):
        """创建预览表格"""
        # 创建表格框架
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ('期号', '开奖日期', '开奖号码', '类型')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)
        
        # 设置列标题和宽度
        for col in columns:
            self.tree.heading(col, text=col)
            if col == '开奖号码':
                self.tree.column(col, width=200)
            else:
                self.tree.column(col, width=100)
        
        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
    
    def get_lottery_name(self) -> str:
        """获取彩票类型名称"""
        names = {
            'dlt': '大乐透',
            'ssq': '双色球',
            'pl5': '排列五'
        }
        return names.get(self.lottery_type, '未知')
    
    def browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.file_path = file_path
            self.path_var.set(file_path)
            self.preview_file(file_path)
    
    def preview_file(self, file_path: str):
        """预览文件内容"""
        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 读取文件前几行
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                rows = []
                for i, row in enumerate(reader):
                    if i >= 10:  # 只预览前10行
                        break
                    rows.append(row)
            
            if not rows:
                self.info_label.config(text="文件为空")
                return
            
            # 显示数据
            for row in rows:
                if len(row) >= 3:  # 至少有期号、日期、号码
                    # 格式化显示
                    issue = row[0] if len(row) > 0 else ""
                    date = row[1] if len(row) > 1 else ""
                    numbers = ",".join(row[2:]) if len(row) > 2 else ""
                    lottery_type = self.get_lottery_name()
                    
                    self.tree.insert('', tk.END, values=(issue, date, numbers, lottery_type))
            
            # 更新信息
            file_size = os.path.getsize(file_path)
            self.info_label.config(text=f"文件大小: {file_size} 字节, 预览前10行数据")
            
        except Exception as e:
            self.info_label.config(text=f"文件读取错误: {str(e)}")
            messagebox.showerror("错误", f"无法读取文件: {str(e)}")
    
    def confirm(self):
        """确认导入"""
        if not self.file_path:
            messagebox.showwarning("警告", "请先选择文件")
            return
        
        self.result = self.file_path
        self.close()
    
    def cancel(self):
        """取消导入"""
        self.result = None
        self.close()
    
    def close(self):
        """关闭对话框"""
        self.dialog.grab_release()
        self.dialog.destroy()


class AnalysisOptionsDialog:
    """分析选项配置对话框"""
    
    def __init__(self, parent, current_options: Dict[str, bool]):
        """初始化分析选项对话框"""
        self.parent = parent
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("分析选项配置")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        
        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建变量
        self.options = {}
        for key, value in current_options.items():
            self.options[key] = tk.BooleanVar(value=value)
        
        # 创建控件
        self.create_widgets()
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")
    
    def create_widgets(self):
        """创建控件"""
        # 标题
        title_label = ttk.Label(
            self.dialog,
            text="选择分析选项",
            font=('Arial', 12, 'bold')
        )
        title_label.pack(pady=20)
        
        # 选项框架
        options_frame = ttk.LabelFrame(self.dialog, text="分析选项", padding=20)
        options_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 分析选项
        option_configs = [
            ('odd_even', '奇偶分析', '分析号码的奇偶分布规律'),
            ('big_small', '大小分析', '分析号码的大小分布规律'),
            ('missing', '号码遗漏分析', '分析各号码的遗漏期数'),
            ('zone_ratio', '分区比分析', '分析号码的分区分布比例')
        ]
        
        for key, text, desc in option_configs:
            if key in self.options:
                frame = ttk.Frame(options_frame)
                frame.pack(fill=tk.X, pady=5)
                
                ttk.Checkbutton(
                    frame,
                    text=text,
                    variable=self.options[key]
                ).pack(side=tk.LEFT)
                
                ttk.Label(
                    frame,
                    text=f"({desc})",
                    font=('Arial', 8),
                    foreground='gray'
                ).pack(side=tk.LEFT, padx=(10, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=20)
        
        ttk.Button(button_frame, text="全选", command=self.select_all).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="全不选", command=self.select_none).pack(side=tk.LEFT, padx=(10, 0))
        
        ttk.Button(button_frame, text="确定", command=self.confirm).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT, padx=(0, 10))
    
    def select_all(self):
        """全选"""
        for var in self.options.values():
            var.set(True)
    
    def select_none(self):
        """全不选"""
        for var in self.options.values():
            var.set(False)
    
    def confirm(self):
        """确认选择"""
        # 检查是否至少选择了一个选项
        selected = any(var.get() for var in self.options.values())
        if not selected:
            messagebox.showwarning("警告", "请至少选择一个分析选项")
            return
        
        # 返回选择结果
        self.result = {key: var.get() for key, var in self.options.items()}
        self.close()
    
    def cancel(self):
        """取消选择"""
        self.result = None
        self.close()
    
    def close(self):
        """关闭对话框"""
        self.dialog.grab_release()
        self.dialog.destroy()


class ResultExportDialog:
    """结果导出对话框"""
    
    def __init__(self, parent, results_text: str):
        """初始化结果导出对话框"""
        self.parent = parent
        self.results_text = results_text
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("导出分析结果")
        self.dialog.geometry("500x400")
        self.dialog.resizable(True, True)
        
        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建控件
        self.create_widgets()
    
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
    
    def create_widgets(self):
        """创建控件"""
        # 标题
        title_label = ttk.Label(
            self.dialog,
            text="导出分析结果",
            font=('Arial', 12, 'bold')
        )
        title_label.pack(pady=10)
        
        # 导出选项框架
        options_frame = ttk.LabelFrame(self.dialog, text="导出选项", padding=10)
        options_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 文件格式选择
        format_frame = ttk.Frame(options_frame)
        format_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(format_frame, text="文件格式:").pack(side=tk.LEFT)
        
        self.format_var = tk.StringVar(value="txt")
        ttk.Radiobutton(format_frame, text="文本文件 (.txt)", variable=self.format_var, value="txt").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Radiobutton(format_frame, text="CSV文件 (.csv)", variable=self.format_var, value="csv").pack(side=tk.LEFT, padx=(10, 0))
        
        # 内容选择
        content_frame = ttk.Frame(options_frame)
        content_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(content_frame, text="导出内容:").pack(side=tk.LEFT)
        
        self.include_header = tk.BooleanVar(value=True)
        self.include_summary = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(content_frame, text="包含标题", variable=self.include_header).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Checkbutton(content_frame, text="包含摘要", variable=self.include_summary).pack(side=tk.LEFT, padx=(10, 0))
        
        # 预览框架
        preview_frame = ttk.LabelFrame(self.dialog, text="内容预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 预览文本框
        self.preview_text = tk.Text(
            preview_frame,
            wrap=tk.WORD,
            height=15,
            font=('Consolas', 9)
        )
        
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)
        
        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 显示预览内容
        self.update_preview()
        
        # 按钮框架
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="导出", command=self.export).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT, padx=(0, 10))
    
    def update_preview(self):
        """更新预览内容"""
        self.preview_text.delete(1.0, tk.END)
        
        content = ""
        if self.include_header.get():
            content += "彩票号码分析系统 - 分析结果\n"
            content += "=" * 50 + "\n\n"
        
        content += self.results_text
        
        if self.include_summary.get():
            content += "\n\n" + "=" * 50
            content += "\n导出时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        self.preview_text.insert(1.0, content)
    
    def export(self):
        """导出文件"""
        try:
            # 选择保存位置
            file_types = [("文本文件", "*.txt"), ("所有文件", "*.*")]
            if self.format_var.get() == "csv":
                file_types = [("CSV文件", "*.csv"), ("所有文件", "*.*")]
            
            file_path = filedialog.asksaveasfilename(
                title="保存分析结果",
                defaultextension=f".{self.format_var.get()}",
                filetypes=file_types
            )
            
            if file_path:
                # 获取导出内容
                content = self.preview_text.get(1.0, "end-1c")
                
                # 保存文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.result = file_path
                messagebox.showinfo("成功", f"结果已导出到: {file_path}")
                self.close()
                
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")
    
    def cancel(self):
        """取消导出"""
        self.result = None
        self.close()
    
    def close(self):
        """关闭对话框"""
        self.dialog.grab_release()
        self.dialog.destroy()


if __name__ == "__main__":
    # 测试对话框
    root = tk.Tk()
    root.withdraw()
    
    # 测试文件导入对话框
    def test_import():
        dialog = FileImportDialog(root, "dlt")
        root.wait_window(dialog.dialog)
        if dialog.result:
            print(f"选择的文件: {dialog.result}")
    
    # 测试分析选项对话框
    def test_options():
        options = {'odd_even': True, 'big_small': True, 'missing': False, 'zone_ratio': True}
        dialog = AnalysisOptionsDialog(root, options)
        root.wait_window(dialog.dialog)
        if dialog.result:
            print(f"选择的选项: {dialog.result}")
    
    # 测试导出对话框
    def test_export():
        results = "这是测试结果\n包含多行内容\n用于测试导出功能"
        dialog = ResultExportDialog(root, results)
        root.wait_window(dialog.dialog)
        if dialog.result:
            print(f"导出到: {dialog.result}")
    
    test_import()
    # test_options()
    # test_export()
    
    root.destroy()
