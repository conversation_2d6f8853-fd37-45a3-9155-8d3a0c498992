"""
简化版主窗口界面

重新设计的简化版彩票数据显示界面，只保留核心功能。
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
from typing import List, Optional, Callable
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.services.lottery_service import LotteryService

logger = logging.getLogger(__name__)


class SimpleMainWindow:
    """简化版主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        self.root = tk.Tk()
        self.lottery_service = LotteryService()
        self.setup_window()
        self.create_variables()
        self.create_widgets()
        self.setup_layout()
        self.setup_bindings()

        # 加载数据
        self.load_data()
    
    def setup_window(self):
        """设置窗口基本属性"""
        self.root.title("彩票数据分析系统 - 简化版")
        self.root.geometry("1200x700")
        self.root.minsize(1000, 600)
    
    def create_variables(self):
        """创建界面变量"""
        self.lottery_type_var = tk.StringVar(value="dlt")
        self.status_var = tk.StringVar(value="就绪")
    
    def create_widgets(self):
        """创建界面控件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 工具栏框架
        self.toolbar_frame = ttk.Frame(self.main_frame)
        
        # 彩票类型选择
        ttk.Label(self.toolbar_frame, text="彩票类型:").pack(side=tk.LEFT, padx=5)
        self.lottery_type_combo = ttk.Combobox(
            self.toolbar_frame, 
            textvariable=self.lottery_type_var,
            values=[("dlt", "大乐透"), ("ssq", "双色球")],
            state="readonly",
            width=10
        )
        self.lottery_type_combo.pack(side=tk.LEFT, padx=5)
        
        # 按钮
        self.refresh_btn = ttk.Button(
            self.toolbar_frame, 
            text="刷新数据", 
            command=self.refresh_data
        )
        self.refresh_btn.pack(side=tk.LEFT, padx=5)
        
        self.import_btn = ttk.Button(
            self.toolbar_frame, 
            text="导入数据", 
            command=self.import_data
        )
        self.import_btn.pack(side=tk.LEFT, padx=5)
        
        self.export_btn = ttk.Button(
            self.toolbar_frame, 
            text="导出数据", 
            command=self.export_data
        )
        self.export_btn.pack(side=tk.LEFT, padx=5)
        
        # 数据表格框架
        self.table_frame = ttk.Frame(self.main_frame)
        
        # 创建数据表格
        self.create_data_table()
        
        # 状态栏
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_label = ttk.Label(
            self.status_frame, 
            textvariable=self.status_var,
            relief=tk.SUNKEN
        )
        self.status_label.pack(fill=tk.X, padx=2, pady=2)
    
    def create_data_table(self):
        """创建数据表格"""
        # 定义列
        columns = [
            ("draw_date", "开奖日期", 100),
            ("issue_number", "期号", 80),
            ("all_numbers", "开奖号码", 200),
            ("front_numbers", "红球", 150),
            ("back_numbers", "篮球", 80),
            ("odd_even_ratio", "奇偶比", 60),
            ("odd_even_pattern", "奇偶排布", 80),
            ("last_odd_even", "上次奇偶排布", 120),
            ("big_small_ratio", "大小比", 60),
            ("big_small_pattern", "大小排布", 80),
            ("last_big_small", "上次大小排布", 120)
        ]
        
        # 创建Treeview
        self.data_tree = ttk.Treeview(
            self.table_frame,
            columns=[col[0] for col in columns],
            show="headings",
            height=20
        )
        
        # 设置列标题和宽度
        for col_id, col_name, col_width in columns:
            self.data_tree.heading(col_id, text=col_name)
            self.data_tree.column(col_id, width=col_width, minwidth=50)
        
        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(self.table_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        h_scrollbar = ttk.Scrollbar(self.table_frame, orient=tk.HORIZONTAL, command=self.data_tree.xview)
        
        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局表格和滚动条
        self.data_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # 配置网格权重
        self.table_frame.grid_rowconfigure(0, weight=1)
        self.table_frame.grid_columnconfigure(0, weight=1)
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.toolbar_frame.pack(fill=tk.X, pady=(0, 5))
        self.table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        self.status_frame.pack(fill=tk.X)
    
    def setup_bindings(self):
        """设置事件绑定"""
        self.lottery_type_combo.bind("<<ComboboxSelected>>", self.on_lottery_type_changed)
    
    def on_lottery_type_changed(self, event=None):
        """彩票类型改变事件"""
        self.load_data()
    
    def load_data(self):
        """加载数据"""
        try:
            self.status_var.set("正在加载数据...")
            self.root.update()
            
            lottery_type = self.lottery_type_var.get()
            records = self.lottery_service.get_records(lottery_type, order_by="draw_date DESC")
            
            self.populate_data_table(records)
            
            count = len(records)
            lottery_name = "大乐透" if lottery_type == "dlt" else "双色球"
            self.status_var.set(f"已加载 {lottery_name} {count} 条记录")
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            self.status_var.set(f"加载数据失败: {e}")
            messagebox.showerror("错误", f"加载数据失败: {e}")
    
    def populate_data_table(self, records: List[LotteryRecord]):
        """填充数据表格"""
        # 清空现有数据
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)
        
        # 添加数据
        for record in records:
            # 格式化日期
            date_str = record.draw_date.strftime('%Y-%m-%d') if record.draw_date else ''
            
            # 格式化号码
            all_numbers = record.format_all_numbers()
            front_numbers = record.format_front_numbers()
            back_numbers = record.format_back_numbers()
            
            # 格式化上次模式
            last_odd_even = record.format_last_odd_even_pattern()
            last_big_small = record.format_last_big_small_pattern()
            
            # 插入行
            self.data_tree.insert('', tk.END, values=(
                date_str,
                record.issue_number,
                all_numbers,
                front_numbers,
                back_numbers,
                record.odd_even_ratio,
                record.odd_even_pattern,
                last_odd_even,
                record.big_small_ratio,
                record.big_small_pattern,
                last_big_small
            ))
    
    def refresh_data(self):
        """刷新数据"""
        self.load_data()
    
    def import_data(self):
        """导入数据"""
        try:
            # 选择数据文件
            file_path = filedialog.askopenfilename(
                title="选择数据文件",
                filetypes=[
                    ("文本文件", "*.txt"),
                    ("所有文件", "*.*")
                ]
            )
            
            if not file_path:
                return
            
            # 确定彩票类型
            lottery_type = self.lottery_type_var.get()
            
            self.status_var.set("正在导入数据...")
            self.root.update()
            
            # 执行数据导入
            count = self.lottery_service.import_data_from_file(file_path, lottery_type)
            
            # 刷新显示
            self.load_data()
            
            lottery_name = "大乐透" if lottery_type == "dlt" else "双色球"
            self.status_var.set(f"成功导入 {lottery_name} {count} 条记录")
            messagebox.showinfo("成功", f"成功导入 {count} 条记录")
            
        except Exception as e:
            logger.error(f"导入数据失败: {e}")
            self.status_var.set(f"导入数据失败: {e}")
            messagebox.showerror("错误", f"导入数据失败: {e}")
    
    def export_data(self):
        """导出数据"""
        try:
            # 选择保存文件
            file_path = filedialog.asksaveasfilename(
                title="导出数据",
                defaultextension=".csv",
                filetypes=[
                    ("CSV文件", "*.csv"),
                    ("文本文件", "*.txt"),
                    ("所有文件", "*.*")
                ]
            )
            
            if not file_path:
                return
            
            self.status_var.set("正在导出数据...")
            self.root.update()
            
            lottery_type = self.lottery_type_var.get()
            records = self.lottery_service.get_records(lottery_type, order_by="draw_date ASC")
            
            # 写入CSV文件
            with open(file_path, 'w', encoding='utf-8') as f:
                # 写入标题行
                headers = [
                    "开奖日期", "期号", "开奖号码", "红球", "篮球", 
                    "奇偶比", "奇偶排布", "上次奇偶排布",
                    "大小比", "大小排布", "上次大小排布"
                ]
                f.write(','.join(headers) + '\n')
                
                # 写入数据行
                for record in records:
                    row = [
                        record.draw_date.strftime('%Y-%m-%d') if record.draw_date else '',
                        record.issue_number,
                        record.format_all_numbers(),
                        record.format_front_numbers(),
                        record.format_back_numbers(),
                        record.odd_even_ratio,
                        record.odd_even_pattern,
                        record.format_last_odd_even_pattern(),
                        record.big_small_ratio,
                        record.big_small_pattern,
                        record.format_last_big_small_pattern()
                    ]
                    f.write(','.join(str(x) for x in row) + '\n')
            
            count = len(records)
            self.status_var.set(f"成功导出 {count} 条记录到 {file_path}")
            messagebox.showinfo("成功", f"成功导出 {count} 条记录")
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            self.status_var.set(f"导出数据失败: {e}")
            messagebox.showerror("错误", f"导出数据失败: {e}")
    
    def run(self):
        """运行主窗口"""
        self.root.mainloop()
    
    def destroy(self):
        """销毁窗口"""
        if self.lottery_service:
            self.lottery_service.close()
        self.root.destroy()


def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        app = SimpleMainWindow()
        app.run()
    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        messagebox.showerror("错误", f"应用程序启动失败: {e}")


if __name__ == "__main__":
    main()
