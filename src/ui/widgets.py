#!/usr/bin/env python3
"""
自定义控件模块
提供彩票号码分析系统专用的UI控件
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Optional, Callable


class ProgressDialog:
    """进度对话框"""
    
    def __init__(self, parent, title="处理中...", message="请稍候..."):
        """初始化进度对话框"""
        self.parent = parent
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("300x120")
        self.dialog.resizable(False, False)
        
        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建控件
        self.create_widgets(message)
        
        # 取消按钮回调
        self.on_cancel: Optional[Callable] = None
        
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (300 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (120 // 2)
        self.dialog.geometry(f"300x120+{x}+{y}")
    
    def create_widgets(self, message):
        """创建控件"""
        # 消息标签
        self.message_label = ttk.Label(
            self.dialog, 
            text=message,
            font=('Arial', 10)
        )
        self.message_label.pack(pady=20)
        
        # 进度条
        self.progress = ttk.Progressbar(
            self.dialog,
            mode='indeterminate',
            length=250
        )
        self.progress.pack(pady=10)
        
        # 取消按钮
        self.cancel_button = ttk.Button(
            self.dialog,
            text="取消",
            command=self.cancel
        )
        self.cancel_button.pack(pady=10)
        
        # 开始进度动画
        self.progress.start(10)
    
    def update_message(self, message: str):
        """更新消息"""
        self.message_label.config(text=message)
        self.dialog.update()
    
    def cancel(self):
        """取消操作"""
        if self.on_cancel:
            self.on_cancel()
        self.close()
    
    def close(self):
        """关闭对话框"""
        self.progress.stop()
        self.dialog.grab_release()
        self.dialog.destroy()


class ResultsViewer:
    """结果查看器"""
    
    def __init__(self, parent):
        """初始化结果查看器"""
        self.parent = parent
        self.create_widgets()
        
    def create_widgets(self):
        """创建控件"""
        # 创建Notebook用于分页显示
        self.notebook = ttk.Notebook(self.parent)
        
        # 文本结果页
        self.text_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.text_frame, text="文本结果")
        
        # 创建文本显示区
        self.text_widget = tk.Text(
            self.text_frame,
            wrap=tk.WORD,
            font=('Consolas', 10)
        )
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.text_frame, orient=tk.VERTICAL, command=self.text_widget.yview)
        self.text_widget.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 图表结果页（预留）
        self.chart_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.chart_frame, text="图表结果")
        
        # 图表占位标签
        ttk.Label(
            self.chart_frame, 
            text="图表功能待实现",
            font=('Arial', 12)
        ).pack(expand=True)
        
        self.notebook.pack(fill=tk.BOTH, expand=True)
    
    def set_text_results(self, text: str):
        """设置文本结果"""
        self.text_widget.delete(1.0, tk.END)
        self.text_widget.insert(1.0, text)
    
    def append_text_results(self, text: str):
        """追加文本结果"""
        self.text_widget.insert(tk.END, text)
        self.text_widget.see(tk.END)
    
    def clear_results(self):
        """清除结果"""
        self.text_widget.delete(1.0, tk.END)


class DataPreviewDialog:
    """数据预览对话框"""
    
    def __init__(self, parent, data_preview: List[List[str]], title="数据预览"):
        """初始化数据预览对话框"""
        self.parent = parent
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("600x400")
        
        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建控件
        self.create_widgets(data_preview)
        
    def center_window(self):
        """窗口居中"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"600x400+{x}+{y}")
    
    def create_widgets(self, data_preview: List[List[str]]):
        """创建控件"""
        # 说明标签
        info_label = ttk.Label(
            self.dialog,
            text="数据预览（前10行）：",
            font=('Arial', 10, 'bold')
        )
        info_label.pack(pady=10)
        
        # 创建表格框架
        table_frame = ttk.Frame(self.dialog)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建Treeview表格
        if data_preview:
            columns = [f"列{i+1}" for i in range(len(data_preview[0]))]
            self.tree = ttk.Treeview(table_frame, columns=columns, show='headings')
            
            # 设置列标题
            for col in columns:
                self.tree.heading(col, text=col)
                self.tree.column(col, width=80)
            
            # 添加数据
            for row in data_preview[:10]:  # 只显示前10行
                self.tree.insert('', tk.END, values=row)
            
            # 添加滚动条
            v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
            h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
            self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
            
            # 布局
            self.tree.grid(row=0, column=0, sticky='nsew')
            v_scrollbar.grid(row=0, column=1, sticky='ns')
            h_scrollbar.grid(row=1, column=0, sticky='ew')
            
            table_frame.grid_rowconfigure(0, weight=1)
            table_frame.grid_columnconfigure(0, weight=1)
        
        # 按钮框架
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(pady=10)
        
        ttk.Button(
            button_frame,
            text="确定",
            command=self.close
        ).pack(side=tk.LEFT, padx=10)
        
        ttk.Button(
            button_frame,
            text="取消",
            command=self.close
        ).pack(side=tk.LEFT, padx=10)
    
    def close(self):
        """关闭对话框"""
        self.dialog.grab_release()
        self.dialog.destroy()


class StatusBar:
    """状态栏"""
    
    def __init__(self, parent):
        """初始化状态栏"""
        self.parent = parent
        self.create_widgets()
        
    def create_widgets(self):
        """创建控件"""
        self.frame = ttk.Frame(self.parent)
        
        # 状态标签
        self.status_label = ttk.Label(
            self.frame,
            text="就绪",
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # 进度标签
        self.progress_label = ttk.Label(
            self.frame,
            text="",
            relief=tk.SUNKEN,
            anchor=tk.E,
            width=20
        )
        self.progress_label.pack(side=tk.RIGHT)
        
        self.frame.pack(side=tk.BOTTOM, fill=tk.X)
    
    def set_status(self, text: str):
        """设置状态文本"""
        self.status_label.config(text=text)
    
    def set_progress(self, text: str):
        """设置进度文本"""
        self.progress_label.config(text=text)
    
    def clear(self):
        """清除状态"""
        self.status_label.config(text="就绪")
        self.progress_label.config(text="")


if __name__ == "__main__":
    # 测试自定义控件
    root = tk.Tk()
    root.title("控件测试")
    root.geometry("400x300")
    
    # 测试状态栏
    status_bar = StatusBar(root)
    status_bar.set_status("测试状态")
    status_bar.set_progress("50%")
    
    # 测试按钮
    def test_progress():
        progress = ProgressDialog(root, "测试进度", "正在处理...")
        root.after(3000, progress.close)  # 3秒后自动关闭
    
    ttk.Button(root, text="测试进度对话框", command=test_progress).pack(pady=20)
    
    root.mainloop()
