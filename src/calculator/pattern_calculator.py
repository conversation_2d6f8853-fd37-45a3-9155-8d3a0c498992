"""
模式计算器

提供"上次模式"计算功能，包括上次奇偶排布和上次分区比排布的计算。
"""

from typing import List, Optional, Tuple
from datetime import datetime
import logging

from ..database.models import LotteryRecord
from .basic_calculator import BasicCalculator

logger = logging.getLogger(__name__)


class PatternCalculator:
    """
    模式计算器类
    
    负责计算"上次模式"相关的功能。
    """
    
    @staticmethod
    def find_last_same_pattern(current_record: LotteryRecord, all_records: List[LotteryRecord], 
                              pattern_type: str) -> Tuple[Optional[datetime], Optional[int]]:
        """
        查找上次相同模式的出现
        
        Args:
            current_record (LotteryRecord): 当前记录
            all_records (List[LotteryRecord]): 所有历史记录（按时间正序排列）
            pattern_type (str): 模式类型 ('odd_even' 或 'big_small' 或 'zone')
            
        Returns:
            Tuple[Optional[datetime], Optional[int]]: (上次出现日期, 间隔期数)
        """
        try:
            # 获取当前记录的模式
            if pattern_type == 'odd_even':
                current_pattern = current_record.odd_even_pattern
            elif pattern_type == 'big_small':
                current_pattern = current_record.big_small_pattern
            elif pattern_type == 'zone':
                # 对于分区模式，需要重新计算
                current_pattern = BasicCalculator.generate_zone_pattern(
                    current_record.front_numbers, current_record.lottery_type, 'front'
                )
            else:
                logger.warning(f"不支持的模式类型: {pattern_type}")
                return None, None
            
            if not current_pattern:
                return None, None
            
            # 找到当前记录在历史记录中的位置
            current_index = -1
            for i, record in enumerate(all_records):
                if (record.lottery_type == current_record.lottery_type and 
                    record.issue_number == current_record.issue_number):
                    current_index = i
                    break
            
            if current_index == -1:
                logger.warning(f"未找到当前记录: {current_record.lottery_type} {current_record.issue_number}")
                return None, None
            
            # 从当前记录往前查找相同模式
            for i in range(current_index - 1, -1, -1):
                record = all_records[i]
                
                # 只在同类型彩票中查找
                if record.lottery_type != current_record.lottery_type:
                    continue
                
                # 获取历史记录的模式
                if pattern_type == 'odd_even':
                    record_pattern = record.odd_even_pattern
                elif pattern_type == 'big_small':
                    record_pattern = record.big_small_pattern
                elif pattern_type == 'zone':
                    record_pattern = BasicCalculator.generate_zone_pattern(
                        record.front_numbers, record.lottery_type, 'front'
                    )
                
                # 如果模式相同，计算间隔
                if record_pattern == current_pattern:
                    interval = current_index - i
                    return record.draw_date, interval
            
            # 没有找到相同模式
            return None, None
            
        except Exception as e:
            logger.error(f"查找上次相同模式失败: {e}")
            return None, None
    
    @staticmethod
    def calculate_all_last_patterns(current_record: LotteryRecord, 
                                  all_records: List[LotteryRecord]) -> dict:
        """
        计算所有上次模式
        
        Args:
            current_record (LotteryRecord): 当前记录
            all_records (List[LotteryRecord]): 所有历史记录
            
        Returns:
            dict: 包含所有上次模式信息的字典
        """
        result = {}
        
        # 计算上次奇偶排布
        odd_even_date, odd_even_interval = PatternCalculator.find_last_same_pattern(
            current_record, all_records, 'odd_even'
        )
        result['last_odd_even_pattern_date'] = odd_even_date
        result['last_odd_even_pattern_interval'] = odd_even_interval
        
        # 计算上次大小排布（这里实际上是分区比排布，根据需求描述）
        zone_date, zone_interval = PatternCalculator.find_last_same_pattern(
            current_record, all_records, 'zone'
        )
        result['last_big_small_pattern_date'] = zone_date
        result['last_big_small_pattern_interval'] = zone_interval
        
        return result
    
    @staticmethod
    def update_record_with_last_patterns(record: LotteryRecord, all_records: List[LotteryRecord]):
        """
        更新记录的上次模式信息
        
        Args:
            record (LotteryRecord): 要更新的记录
            all_records (List[LotteryRecord]): 所有历史记录
        """
        last_patterns = PatternCalculator.calculate_all_last_patterns(record, all_records)
        
        record.last_odd_even_pattern_date = last_patterns['last_odd_even_pattern_date']
        record.last_odd_even_pattern_interval = last_patterns['last_odd_even_pattern_interval']
        record.last_big_small_pattern_date = last_patterns['last_big_small_pattern_date']
        record.last_big_small_pattern_interval = last_patterns['last_big_small_pattern_interval']
    
    @staticmethod
    def batch_update_last_patterns(records: List[LotteryRecord]):
        """
        批量更新记录的上次模式信息
        
        Args:
            records (List[LotteryRecord]): 记录列表（应按时间正序排列）
        """
        logger.info(f"开始批量更新 {len(records)} 条记录的上次模式信息")
        
        for i, record in enumerate(records):
            try:
                # 使用当前记录之前的所有记录来计算上次模式
                previous_records = records[:i+1]  # 包含当前记录
                PatternCalculator.update_record_with_last_patterns(record, previous_records)
                
                if (i + 1) % 100 == 0:
                    logger.info(f"已处理 {i + 1}/{len(records)} 条记录")
                    
            except Exception as e:
                logger.error(f"更新记录 {record.lottery_type} {record.issue_number} 的上次模式失败: {e}")
        
        logger.info("批量更新上次模式信息完成")
    
    @staticmethod
    def get_pattern_decimal_value(record: LotteryRecord, pattern_type: str) -> Optional[int]:
        """
        获取模式的十进制值
        
        Args:
            record (LotteryRecord): 彩票记录
            pattern_type (str): 模式类型 ('odd_even' 或 'zone')
            
        Returns:
            Optional[int]: 模式的十进制值
        """
        try:
            if pattern_type == 'odd_even':
                pattern = record.odd_even_pattern
            elif pattern_type == 'zone':
                pattern = BasicCalculator.generate_zone_pattern(
                    record.front_numbers, record.lottery_type, 'front'
                )
            else:
                return None
            
            if pattern:
                return BasicCalculator.pattern_to_decimal(pattern)
            
            return None
            
        except Exception as e:
            logger.error(f"获取模式十进制值失败: {e}")
            return None
    
    @staticmethod
    def validate_pattern_calculation(record: LotteryRecord) -> bool:
        """
        验证模式计算的正确性
        
        Args:
            record (LotteryRecord): 彩票记录
            
        Returns:
            bool: 计算是否正确
        """
        try:
            # 验证奇偶排布
            expected_odd_even = BasicCalculator.generate_odd_even_pattern(record.front_numbers)
            if record.odd_even_pattern != expected_odd_even:
                logger.warning(f"奇偶排布不匹配: 期望 {expected_odd_even}, 实际 {record.odd_even_pattern}")
                return False
            
            # 验证大小排布
            expected_big_small = BasicCalculator.generate_big_small_pattern(
                record.front_numbers, record.lottery_type, 'front'
            )
            if record.big_small_pattern != expected_big_small:
                logger.warning(f"大小排布不匹配: 期望 {expected_big_small}, 实际 {record.big_small_pattern}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证模式计算失败: {e}")
            return False
