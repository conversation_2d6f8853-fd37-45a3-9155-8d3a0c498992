"""
基础计算器

提供彩票数据的基础计算功能，包括奇偶比、大小比、模式生成等。
"""

from typing import List, Tuple
from .lottery_config import (
    is_odd, is_big, count_odd_even, count_big_small, count_zone_distribution,
    get_lottery_config
)


class BasicCalculator:
    """
    基础计算器类
    
    提供彩票数据的基础计算功能。
    """
    
    @staticmethod
    def calculate_odd_even_ratio(numbers: List[int]) -> str:
        """
        计算奇偶比
        
        Args:
            numbers (List[int]): 号码列表
            
        Returns:
            str: 奇偶比，格式如 "3:2"
        """
        odd_count, even_count = count_odd_even(numbers)
        return f"{odd_count}:{even_count}"
    
    @staticmethod
    def generate_odd_even_pattern(numbers: List[int]) -> str:
        """
        生成奇偶排布模式
        
        Args:
            numbers (List[int]): 号码列表
            
        Returns:
            str: 奇偶排布，格式如 "10110" (1表示奇数，0表示偶数)
        """
        return ''.join('1' if is_odd(num) else '0' for num in numbers)
    
    @staticmethod
    def calculate_big_small_ratio(numbers: List[int], lottery_type: str, zone_type: str = 'front') -> str:
        """
        计算大小比
        
        Args:
            numbers (List[int]): 号码列表
            lottery_type (str): 彩票类型
            zone_type (str): 区域类型
            
        Returns:
            str: 大小比，格式如 "2:3"
        """
        big_count, small_count = count_big_small(numbers, lottery_type, zone_type)
        return f"{big_count}:{small_count}"
    
    @staticmethod
    def generate_big_small_pattern(numbers: List[int], lottery_type: str, zone_type: str = 'front') -> str:
        """
        生成大小排布模式
        
        Args:
            numbers (List[int]): 号码列表
            lottery_type (str): 彩票类型
            zone_type (str): 区域类型
            
        Returns:
            str: 大小排布，格式如 "01101" (1表示大号，0表示小号)
        """
        return ''.join('1' if is_big(num, lottery_type, zone_type) else '0' for num in numbers)
    
    @staticmethod
    def generate_zone_pattern(numbers: List[int], lottery_type: str, zone_type: str = 'front') -> str:
        """
        生成分区排布模式
        
        Args:
            numbers (List[int]): 号码列表
            lottery_type (str): 彩票类型
            zone_type (str): 区域类型
            
        Returns:
            str: 分区排布，格式如 "1110110" (1表示该分区有号码，0表示没有)
        """
        zone_count = count_zone_distribution(numbers, lottery_type, zone_type)
        config = get_lottery_config(lottery_type)
        
        if zone_type == 'front':
            zones = config['front_zones']
        else:
            zones = config['back_zones']
        
        # 按分区顺序生成二进制模式
        pattern = ''.join('1' if zone_count.get(zone['name'], 0) > 0 else '0' for zone in zones)
        return pattern
    
    @staticmethod
    def pattern_to_decimal(pattern: str) -> int:
        """
        将二进制模式转换为十进制
        
        Args:
            pattern (str): 二进制模式字符串
            
        Returns:
            int: 十进制数值
        """
        if not pattern or not all(c in '01' for c in pattern):
            return 0
        
        return int(pattern, 2)
    
    @staticmethod
    def calculate_all_attributes(front_numbers: List[int], back_numbers: List[int], 
                               lottery_type: str) -> dict:
        """
        计算所有基础属性
        
        Args:
            front_numbers (List[int]): 前区/红球号码
            back_numbers (List[int]): 后区/蓝球号码
            lottery_type (str): 彩票类型
            
        Returns:
            dict: 包含所有计算属性的字典
        """
        # 只计算前区/红球的属性（根据需求）
        result = {
            # 奇偶属性
            'odd_even_ratio': BasicCalculator.calculate_odd_even_ratio(front_numbers),
            'odd_even_pattern': BasicCalculator.generate_odd_even_pattern(front_numbers),
            
            # 大小属性
            'big_small_ratio': BasicCalculator.calculate_big_small_ratio(front_numbers, lottery_type, 'front'),
            'big_small_pattern': BasicCalculator.generate_big_small_pattern(front_numbers, lottery_type, 'front'),
            
            # 分区属性（用于计算上次分区比排布）
            'zone_pattern': BasicCalculator.generate_zone_pattern(front_numbers, lottery_type, 'front'),
        }
        
        return result
    
    @staticmethod
    def validate_numbers(front_numbers: List[int], back_numbers: List[int], lottery_type: str) -> bool:
        """
        验证号码的有效性
        
        Args:
            front_numbers (List[int]): 前区/红球号码
            back_numbers (List[int]): 后区/蓝球号码
            lottery_type (str): 彩票类型
            
        Returns:
            bool: 号码是否有效
        """
        try:
            config = get_lottery_config(lottery_type)
            
            # 检查前区号码数量
            if len(front_numbers) != config['front_count']:
                return False
            
            # 检查后区号码数量
            if len(back_numbers) != config['back_count']:
                return False
            
            # 检查前区号码范围
            front_min, front_max = config['front_range']
            if not all(front_min <= num <= front_max for num in front_numbers):
                return False
            
            # 检查后区号码范围
            back_min, back_max = config['back_range']
            if not all(back_min <= num <= back_max for num in back_numbers):
                return False
            
            # 检查前区号码是否有重复
            if len(set(front_numbers)) != len(front_numbers):
                return False
            
            # 检查后区号码是否有重复
            if len(set(back_numbers)) != len(back_numbers):
                return False
            
            return True
            
        except Exception:
            return False
