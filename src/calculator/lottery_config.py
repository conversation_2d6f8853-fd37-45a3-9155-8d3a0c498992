"""
彩票类型配置

定义大乐透和双色球的基本配置信息。
"""

from typing import Dict, List, Tuple, Any


# 彩票类型配置
LOTTERY_CONFIGS: Dict[str, Dict[str, Any]] = {
    # 大乐透配置
    'dlt': {
        'name': '大乐透',
        'front_count': 5,           # 前区号码数量
        'back_count': 2,            # 后区号码数量
        'front_range': (1, 35),     # 前区号码范围
        'back_range': (1, 12),      # 后区号码范围
        'front_big_boundary': 18,   # 前区大小分界点（>18为大号）
        'back_big_boundary': 7,     # 后区大小分界点（>7为大号）
        
        # 前区分区定义（7个区）
        'front_zones': [
            {'name': 'zone1', 'range': (1, 5), 'description': '第一区(01-05)'},
            {'name': 'zone2', 'range': (6, 10), 'description': '第二区(06-10)'},
            {'name': 'zone3', 'range': (11, 15), 'description': '第三区(11-15)'},
            {'name': 'zone4', 'range': (16, 20), 'description': '第四区(16-20)'},
            {'name': 'zone5', 'range': (21, 25), 'description': '第五区(21-25)'},
            {'name': 'zone6', 'range': (26, 30), 'description': '第六区(26-30)'},
            {'name': 'zone7', 'range': (31, 35), 'description': '第七区(31-35)'}
        ],
        
        # 后区分区定义（2个区）
        'back_zones': [
            {'name': 'back_zone1', 'range': (1, 6), 'description': '后区小号(01-06)'},
            {'name': 'back_zone2', 'range': (7, 12), 'description': '后区大号(07-12)'}
        ]
    },
    
    # 双色球配置
    'ssq': {
        'name': '双色球',
        'front_count': 6,           # 红球数量
        'back_count': 1,            # 蓝球数量
        'front_range': (1, 33),     # 红球号码范围
        'back_range': (1, 16),      # 蓝球号码范围
        'front_big_boundary': 17,   # 红球大小分界点（>17为大号）
        'back_big_boundary': 9,     # 蓝球大小分界点（>9为大号）
        
        # 红球分区定义（7个区）
        'front_zones': [
            {'name': 'zone1', 'range': (1, 5), 'description': '第一区(01-05)'},
            {'name': 'zone2', 'range': (6, 10), 'description': '第二区(06-10)'},
            {'name': 'zone3', 'range': (11, 15), 'description': '第三区(11-15)'},
            {'name': 'zone4', 'range': (16, 20), 'description': '第四区(16-20)'},
            {'name': 'zone5', 'range': (21, 25), 'description': '第五区(21-25)'},
            {'name': 'zone6', 'range': (26, 30), 'description': '第六区(26-30)'},
            {'name': 'zone7', 'range': (31, 33), 'description': '第七区(31-33)'}
        ],
        
        # 蓝球分区定义（2个区）
        'back_zones': [
            {'name': 'back_zone1', 'range': (1, 8), 'description': '蓝球小号(01-08)'},
            {'name': 'back_zone2', 'range': (9, 16), 'description': '蓝球大号(09-16)'}
        ]
    }
}


def get_lottery_config(lottery_type: str) -> Dict[str, Any]:
    """
    获取彩票类型配置
    
    Args:
        lottery_type (str): 彩票类型 ('dlt', 'ssq')
        
    Returns:
        Dict[str, Any]: 彩票配置信息
        
    Raises:
        ValueError: 不支持的彩票类型
    """
    if lottery_type not in LOTTERY_CONFIGS:
        raise ValueError(f"不支持的彩票类型: {lottery_type}")
    
    return LOTTERY_CONFIGS[lottery_type]


def is_odd(number: int) -> bool:
    """判断数字是否为奇数"""
    return number % 2 == 1


def is_big(number: int, lottery_type: str, zone_type: str = 'front') -> bool:
    """
    判断数字是否为大号
    
    Args:
        number (int): 数字
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型 ('front' 或 'back')
        
    Returns:
        bool: 是否为大号
    """
    config = get_lottery_config(lottery_type)
    
    if zone_type == 'front':
        boundary = config['front_big_boundary']
    else:
        boundary = config['back_big_boundary']
    
    return number > boundary


def get_number_zone(number: int, lottery_type: str, zone_type: str = 'front') -> str:
    """
    获取数字所属的分区
    
    Args:
        number (int): 数字
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型 ('front' 或 'back')
        
    Returns:
        str: 分区名称
    """
    config = get_lottery_config(lottery_type)
    
    if zone_type == 'front':
        zones = config['front_zones']
    else:
        zones = config['back_zones']
    
    for zone in zones:
        start, end = zone['range']
        if start <= number <= end:
            return zone['name']
    
    return 'unknown'


def count_odd_even(numbers: List[int]) -> Tuple[int, int]:
    """
    统计奇偶数量
    
    Args:
        numbers (List[int]): 号码列表
        
    Returns:
        Tuple[int, int]: (奇数数量, 偶数数量)
    """
    odd_count = sum(1 for num in numbers if is_odd(num))
    even_count = len(numbers) - odd_count
    return odd_count, even_count


def count_big_small(numbers: List[int], lottery_type: str, zone_type: str = 'front') -> Tuple[int, int]:
    """
    统计大小号数量
    
    Args:
        numbers (List[int]): 号码列表
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型
        
    Returns:
        Tuple[int, int]: (大号数量, 小号数量)
    """
    big_count = sum(1 for num in numbers if is_big(num, lottery_type, zone_type))
    small_count = len(numbers) - big_count
    return big_count, small_count


def count_zone_distribution(numbers: List[int], lottery_type: str, zone_type: str = 'front') -> Dict[str, int]:
    """
    统计分区分布
    
    Args:
        numbers (List[int]): 号码列表
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型
        
    Returns:
        Dict[str, int]: 各分区的号码数量
    """
    config = get_lottery_config(lottery_type)
    
    if zone_type == 'front':
        zones = config['front_zones']
    else:
        zones = config['back_zones']
    
    # 初始化分区计数
    zone_count = {zone['name']: 0 for zone in zones}
    
    # 统计每个号码所属分区
    for number in numbers:
        zone_name = get_number_zone(number, lottery_type, zone_type)
        if zone_name in zone_count:
            zone_count[zone_name] += 1
    
    return zone_count
