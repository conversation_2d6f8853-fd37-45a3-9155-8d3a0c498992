"""
彩票服务类

提供彩票数据的完整处理服务，包括数据导入、计算、存储和查询。
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..database.db_manager import DatabaseManager
from ..database.models import LotteryRecord
from ..calculator.basic_calculator import BasicCalculator
from ..calculator.pattern_calculator import PatternCalculator
from ..data.reader import read_lottery_data

logger = logging.getLogger(__name__)


class LotteryService:
    """
    彩票服务类
    
    提供彩票数据的完整处理流程，包括数据导入、计算、存储和查询。
    """
    
    def __init__(self, db_path: str = "lottery_data.db"):
        """
        初始化彩票服务
        
        Args:
            db_path (str): 数据库文件路径
        """
        self.db_manager = DatabaseManager(db_path)
        logger.info("彩票服务初始化完成")
    
    def import_data_from_file(self, file_path: str, lottery_type: str) -> int:
        """
        从文件导入彩票数据
        
        Args:
            file_path (str): 数据文件路径
            lottery_type (str): 彩票类型 ('dlt', 'ssq')
            
        Returns:
            int: 导入的记录数量
        """
        logger.info(f"开始从文件导入数据: {file_path}, 类型: {lottery_type}")
        
        try:
            # 读取原始数据
            lottery_data_list = read_lottery_data(file_path, lottery_type)
            logger.info(f"读取到 {len(lottery_data_list)} 条原始数据")
            
            if not lottery_data_list:
                logger.warning("没有读取到数据")
                return 0
            
            # 转换为数据库记录
            records = []
            for lottery_data in lottery_data_list:
                try:
                    record = self._convert_to_record(lottery_data, lottery_type)
                    records.append(record)
                except Exception as e:
                    logger.warning(f"转换记录失败，跳过: {lottery_data.issue_number}, 错误: {e}")
            
            logger.info(f"成功转换 {len(records)} 条记录")
            
            # 处理数据（计算属性和上次模式）
            processed_records = self._process_records(records, lottery_type)
            
            # 插入数据库
            count = self.db_manager.insert_records(processed_records)
            
            logger.info(f"数据导入完成，成功导入 {count} 条记录")
            return count
            
        except Exception as e:
            logger.error(f"数据导入失败: {e}")
            raise
    
    def _convert_to_record(self, lottery_data, lottery_type: str) -> LotteryRecord:
        """
        将LotteryData对象转换为LotteryRecord
        
        Args:
            lottery_data: 原始彩票数据对象
            lottery_type (str): 彩票类型
            
        Returns:
            LotteryRecord: 数据库记录对象
        """
        record = LotteryRecord()
        record.lottery_type = lottery_type
        record.issue_number = lottery_data.issue_number
        record.draw_date = lottery_data.draw_date
        record.front_numbers = lottery_data.get_front_numbers()
        record.back_numbers = lottery_data.get_back_numbers()
        
        # 验证号码有效性
        if not BasicCalculator.validate_numbers(record.front_numbers, record.back_numbers, lottery_type):
            raise ValueError(f"无效的号码数据: {record.front_numbers} + {record.back_numbers}")
        
        return record
    
    def _process_records(self, records: List[LotteryRecord], lottery_type: str) -> List[LotteryRecord]:
        """
        处理记录列表，计算所有属性
        
        Args:
            records (List[LotteryRecord]): 记录列表
            lottery_type (str): 彩票类型
            
        Returns:
            List[LotteryRecord]: 处理后的记录列表
        """
        logger.info(f"开始处理 {len(records)} 条记录")
        
        # 按时间排序
        records.sort(key=lambda x: x.draw_date)
        
        # 获取现有的所有记录（用于计算上次模式）
        existing_records = self.db_manager.get_records(lottery_type, order_by="draw_date ASC")
        all_records = existing_records + records
        all_records.sort(key=lambda x: x.draw_date)
        
        # 处理每条记录
        for i, record in enumerate(records):
            try:
                # 计算基础属性
                self._calculate_basic_attributes(record)
                
                # 计算上次模式（使用所有历史记录）
                record_index = all_records.index(record)
                history_records = all_records[:record_index + 1]
                PatternCalculator.update_record_with_last_patterns(record, history_records)
                
                if (i + 1) % 100 == 0:
                    logger.info(f"已处理 {i + 1}/{len(records)} 条记录")
                    
            except Exception as e:
                logger.error(f"处理记录失败: {record.lottery_type} {record.issue_number}, 错误: {e}")
                raise
        
        logger.info("记录处理完成")
        return records
    
    def _calculate_basic_attributes(self, record: LotteryRecord):
        """
        计算记录的基础属性
        
        Args:
            record (LotteryRecord): 彩票记录
        """
        attributes = BasicCalculator.calculate_all_attributes(
            record.front_numbers, record.back_numbers, record.lottery_type
        )
        
        record.odd_even_ratio = attributes['odd_even_ratio']
        record.odd_even_pattern = attributes['odd_even_pattern']
        record.big_small_ratio = attributes['big_small_ratio']
        record.big_small_pattern = attributes['big_small_pattern']
    
    def add_single_record(self, issue_number: str, draw_date: datetime, 
                         front_numbers: List[int], back_numbers: List[int], 
                         lottery_type: str) -> LotteryRecord:
        """
        添加单条记录
        
        Args:
            issue_number (str): 期号
            draw_date (datetime): 开奖日期
            front_numbers (List[int]): 前区/红球号码
            back_numbers (List[int]): 后区/蓝球号码
            lottery_type (str): 彩票类型
            
        Returns:
            LotteryRecord: 添加的记录
        """
        logger.info(f"添加单条记录: {lottery_type} {issue_number}")
        
        try:
            # 创建记录
            record = LotteryRecord()
            record.lottery_type = lottery_type
            record.issue_number = issue_number
            record.draw_date = draw_date
            record.front_numbers = front_numbers
            record.back_numbers = back_numbers
            
            # 验证号码
            if not BasicCalculator.validate_numbers(front_numbers, back_numbers, lottery_type):
                raise ValueError(f"无效的号码数据: {front_numbers} + {back_numbers}")
            
            # 计算基础属性
            self._calculate_basic_attributes(record)
            
            # 计算上次模式
            all_records = self.db_manager.get_records(lottery_type, order_by="draw_date ASC")
            all_records.append(record)
            all_records.sort(key=lambda x: x.draw_date)
            
            PatternCalculator.update_record_with_last_patterns(record, all_records)
            
            # 插入数据库
            record_id = self.db_manager.insert_record(record)
            record.id = record_id
            
            logger.info(f"成功添加记录: {lottery_type} {issue_number}")
            return record
            
        except Exception as e:
            logger.error(f"添加记录失败: {e}")
            raise
    
    def get_records(self, lottery_type: str = None, limit: int = None, 
                   order_by: str = "draw_date DESC") -> List[LotteryRecord]:
        """
        查询记录
        
        Args:
            lottery_type (str, optional): 彩票类型过滤
            limit (int, optional): 限制返回数量
            order_by (str): 排序方式
            
        Returns:
            List[LotteryRecord]: 彩票记录列表
        """
        return self.db_manager.get_records(lottery_type, limit, order_by)
    
    def get_record_by_issue(self, lottery_type: str, issue_number: str) -> Optional[LotteryRecord]:
        """
        根据期号查询记录
        
        Args:
            lottery_type (str): 彩票类型
            issue_number (str): 期号
            
        Returns:
            Optional[LotteryRecord]: 彩票记录或None
        """
        return self.db_manager.get_record_by_issue(lottery_type, issue_number)
    
    def get_statistics(self, lottery_type: str = None) -> Dict[str, Any]:
        """
        获取统计信息
        
        Args:
            lottery_type (str, optional): 彩票类型过滤
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {}
            
            if lottery_type:
                count = self.db_manager.get_count(lottery_type)
                records = self.db_manager.get_records(lottery_type, limit=1, order_by="draw_date DESC")
                latest_date = records[0].draw_date if records else None
                
                stats[lottery_type] = {
                    'count': count,
                    'latest_date': latest_date.strftime('%Y-%m-%d') if latest_date else None
                }
            else:
                # 所有类型的统计
                for lt in ['dlt', 'ssq']:
                    count = self.db_manager.get_count(lt)
                    records = self.db_manager.get_records(lt, limit=1, order_by="draw_date DESC")
                    latest_date = records[0].draw_date if records else None
                    
                    stats[lt] = {
                        'count': count,
                        'latest_date': latest_date.strftime('%Y-%m-%d') if latest_date else None
                    }
                
                stats['total'] = self.db_manager.get_count()
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def recalculate_all_patterns(self, lottery_type: str = None):
        """
        重新计算所有记录的上次模式
        
        Args:
            lottery_type (str, optional): 彩票类型，None表示所有类型
        """
        logger.info(f"开始重新计算上次模式: {lottery_type or '所有类型'}")
        
        try:
            if lottery_type:
                lottery_types = [lottery_type]
            else:
                lottery_types = ['dlt', 'ssq']
            
            for lt in lottery_types:
                records = self.db_manager.get_records(lt, order_by="draw_date ASC")
                
                if not records:
                    logger.info(f"{lt} 没有记录需要处理")
                    continue
                
                logger.info(f"开始处理 {lt} 的 {len(records)} 条记录")
                
                # 重新计算上次模式
                PatternCalculator.batch_update_last_patterns(records)
                
                # 更新数据库
                for record in records:
                    self.db_manager.update_last_patterns(
                        record.id,
                        record.last_odd_even_pattern_date,
                        record.last_odd_even_pattern_interval,
                        record.last_big_small_pattern_date,
                        record.last_big_small_pattern_interval
                    )
                
                logger.info(f"{lt} 上次模式重新计算完成")
            
            logger.info("所有上次模式重新计算完成")
            
        except Exception as e:
            logger.error(f"重新计算上次模式失败: {e}")
            raise
    
    def clear_all_data(self):
        """清空所有数据"""
        logger.info("清空所有数据")
        self.db_manager.clear_all_data()
    
    def close(self):
        """关闭服务"""
        if self.db_manager:
            self.db_manager.disconnect()
        logger.info("彩票服务已关闭")
