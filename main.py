#!/usr/bin/env python3
"""
彩票号码分析系统主程序
"""

import sys
import os
import logging
from tkinter import messagebox

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lottery_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def check_dependencies():
    """检查依赖包"""
    required_packages = ['tkinter', 'pandas', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'pandas':
                import pandas
            elif package == 'numpy':
                import numpy
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        error_msg = f"缺少必需的依赖包：{', '.join(missing_packages)}\n请使用 pip install 安装这些包。"
        logger.error(error_msg)
        try:
            import tkinter
            from tkinter import messagebox
            root = tkinter.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("依赖错误", error_msg)
        except:
            print(error_msg)
        return False
    
    return True


def main():
    """主函数"""
    try:
        logger.info("启动彩票号码分析系统")
        
        # 检查依赖
        if not check_dependencies():
            return
        
        # 导入应用程序控制器
        from src.ui.app_controller import AppController
        
        # 创建并运行应用程序
        app = AppController()
        app.run()
        
        logger.info("应用程序正常退出")
        
    except Exception as e:
        error_msg = f"应用程序启动失败：{str(e)}"
        logger.error(error_msg, exc_info=True)
        
        try:
            import tkinter
            from tkinter import messagebox
            root = tkinter.Tk()
            root.withdraw()
            messagebox.showerror("启动错误", error_msg)
        except:
            print(error_msg)


if __name__ == "__main__":
    main()
