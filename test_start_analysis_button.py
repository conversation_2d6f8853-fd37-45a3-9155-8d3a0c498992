#!/usr/bin/env python3
"""
测试开始分析按钮功能
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_button_exists():
    """测试按钮是否存在"""
    try:
        from src.ui.main_window import MainWindow
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 检查按钮是否存在
        if hasattr(main_window, 'start_analysis_btn'):
            print("✅ 开始分析按钮已成功添加")
            
            # 检查按钮是否绑定了正确的方法
            if hasattr(main_window, 'start_analysis'):
                print("✅ start_analysis方法存在")
                
                # 测试按钮点击
                print("🔄 测试按钮点击功能...")
                try:
                    # 模拟按钮点击
                    main_window.start_analysis()
                    print("✅ 按钮点击功能正常")
                except Exception as e:
                    print(f"⚠️ 按钮点击测试警告: {e}")
                
            else:
                print("❌ start_analysis方法不存在")
                return False
                
        else:
            print("❌ 开始分析按钮不存在")
            return False
            
        # 检查按钮区域是否存在
        if hasattr(main_window, 'button_frame'):
            print("✅ 按钮区域已成功创建")
        else:
            print("❌ 按钮区域不存在")
            return False
            
        # 关闭窗口
        main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_ui_layout():
    """测试UI布局"""
    try:
        from src.ui.main_window import MainWindow
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 检查所有必要的组件是否存在
        components = [
            ('toolbar_frame', '工具栏'),
            ('lottery_frame', '彩票类型选择区'),
            ('options_frame', '分析选项区'),
            ('button_frame', '按钮区域'),
            ('results_frame', '结果显示区')
        ]
        
        all_exist = True
        for attr, name in components:
            if hasattr(main_window, attr):
                print(f"✅ {name}存在")
            else:
                print(f"❌ {name}不存在")
                all_exist = False
        
        # 关闭窗口
        main_window.root.destroy()
        return all_exist
        
    except Exception as e:
        print(f"❌ UI布局测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("开始分析按钮功能测试")
    print("=" * 50)
    
    # 测试按钮存在性
    print("\n1. 测试按钮存在性...")
    button_test = test_button_exists()
    
    # 测试UI布局
    print("\n2. 测试UI布局...")
    layout_test = test_ui_layout()
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    if button_test and layout_test:
        print("🎉 所有测试通过！开始分析按钮功能正常")
        return True
    else:
        print("❌ 部分测试失败，需要检查实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
