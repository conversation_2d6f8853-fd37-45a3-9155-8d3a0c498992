#!/usr/bin/env python3
"""
数据导入和分析功能集成演示
"""

import sys
import os
import tempfile
import csv
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from src.data.reader import DataReader
from src.data.validator import DataValidator
from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5
from src.analysis.dlt_analyzer import DLTAnalyzer
from src.analysis.ssq_analyzer import SSQAnalyzer
from src.analysis.pl5_analyzer import PL5Analyzer


def create_sample_data_file(lottery_type: str) -> str:
    """创建示例数据文件"""
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
    
    if lottery_type == LOTTERY_TYPE_DLT:
        # 大乐透示例数据
        data = [
            ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"],
            ["24001", "2024-01-01", "1", "5", "15", "25", "35", "2", "8"],
            ["24002", "2024-01-03", "3", "7", "17", "27", "33", "1", "9"],
            ["24003", "2024-01-06", "2", "8", "18", "28", "34", "3", "10"],
            ["24004", "2024-01-08", "4", "9", "19", "29", "31", "5", "11"],
            ["24005", "2024-01-10", "6", "11", "21", "30", "32", "4", "12"],
            ["24006", "2024-01-13", "7", "12", "22", "26", "35", "6", "7"],
            ["24007", "2024-01-15", "1", "10", "20", "24", "33", "8", "9"],
            ["24008", "2024-01-17", "5", "13", "23", "27", "34", "2", "11"],
            ["24009", "2024-01-20", "3", "14", "16", "28", "32", "1", "12"],
            ["24010", "2024-01-22", "8", "15", "25", "29", "31", "4", "6"]
        ]
    elif lottery_type == LOTTERY_TYPE_SSQ:
        # 双色球示例数据
        data = [
            ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "红球6", "蓝球"],
            ["24001", "2024-01-02", "1", "5", "15", "25", "30", "33", "8"],
            ["24002", "2024-01-04", "3", "7", "17", "27", "31", "32", "2"],
            ["24003", "2024-01-07", "2", "8", "18", "28", "29", "33", "15"],
            ["24004", "2024-01-09", "4", "9", "19", "26", "30", "32", "1"],
            ["24005", "2024-01-11", "6", "11", "21", "24", "31", "33", "12"]
        ]
    elif lottery_type == LOTTERY_TYPE_PL5:
        # 排列五示例数据
        data = [
            ["期号", "开奖日期", "万位", "千位", "百位", "十位", "个位"],
            ["24001", "2024-01-01", "1", "2", "3", "4", "5"],
            ["24002", "2024-01-02", "6", "7", "8", "9", "0"],
            ["24003", "2024-01-03", "1", "3", "5", "7", "9"],
            ["24004", "2024-01-04", "2", "4", "6", "8", "0"],
            ["24005", "2024-01-05", "9", "8", "7", "6", "5"]
        ]
    
    writer = csv.writer(temp_file)
    for row in data:
        writer.writerow(row)
    
    temp_file.close()
    return temp_file.name


def demo_data_import_and_validation():
    """演示数据导入和验证"""
    print("=" * 60)
    print("数据导入和验证演示")
    print("=" * 60)
    
    # 创建组件
    data_reader = DataReader()
    data_validator = DataValidator()
    
    # 测试不同彩票类型
    lottery_types = [
        (LOTTERY_TYPE_DLT, "大乐透"),
        (LOTTERY_TYPE_SSQ, "双色球"),
        (LOTTERY_TYPE_PL5, "排列五")
    ]
    
    for lottery_type, name in lottery_types:
        print(f"\n【{name}数据导入测试】")
        print("-" * 40)
        
        try:
            # 创建示例数据文件
            temp_file = create_sample_data_file(lottery_type)
            print(f"创建示例数据文件: {temp_file}")
            
            # 读取数据
            data_list = data_reader.read_csv(temp_file, lottery_type)
            print(f"成功读取 {len(data_list)} 条数据")
            
            # 验证数据
            validation_result = data_validator.validate_data_list(data_list)
            print(f"数据验证完成:")
            print(f"  总数据量: {validation_result['total_count']}")
            print(f"  有效数据: {validation_result['valid_count']}")
            print(f"  无效数据: {validation_result['invalid_count']}")
            
            if validation_result['warnings']:
                print("  警告信息:")
                for warning in validation_result['warnings']:
                    print(f"    - {warning}")
            
            # 清理临时文件
            os.unlink(temp_file)
            
        except Exception as e:
            print(f"测试失败: {str(e)}")


def demo_analysis_integration():
    """演示分析功能集成"""
    print("\n" + "=" * 60)
    print("分析功能集成演示")
    print("=" * 60)
    
    # 创建大乐透示例数据
    temp_file = create_sample_data_file(LOTTERY_TYPE_DLT)
    
    try:
        # 读取和验证数据
        data_reader = DataReader()
        data_validator = DataValidator()
        
        data_list = data_reader.read_csv(temp_file, LOTTERY_TYPE_DLT)
        validation_result = data_validator.validate_data_list(data_list)
        
        print(f"加载了 {validation_result['valid_count']} 条有效的大乐透数据")
        
        # 创建分析器
        analyzer = DLTAnalyzer()
        analyzer.load_data(data_list)
        
        print("\n【分析结果】")
        print("-" * 40)
        
        # 奇偶分析
        print("1. 奇偶分析:")
        odd_even_result = analyzer.analyze_odd_even_distribution('front')
        print(f"   前区总期数: {odd_even_result['total_periods']}")
        print(f"   最新模式: {odd_even_result['latest_pattern']}")
        
        # 大小分析
        print("\n2. 大小分析:")
        big_small_result = analyzer.analyze_big_small_distribution('front')
        print(f"   前区总期数: {big_small_result['total_periods']}")
        print(f"   最新模式: {big_small_result['latest_pattern']}")
        
        # 遗漏分析
        print("\n3. 号码遗漏分析:")
        missing_result = analyzer.calculate_all_numbers_missing('front')
        print(f"   总号码数: {missing_result['total_numbers']}")
        print(f"   总期数: {missing_result['total_periods']}")
        
        if missing_result.get('most_missing'):
            most_missing = missing_result['most_missing']
            print(f"   最大遗漏: 号码{most_missing['number']} (遗漏{most_missing['missing']}期)")
        
        # 分区比分析
        print("\n4. 分区比分析:")
        zone_result = analyzer.analyze_zone_ratio('front')
        print(f"   前区总期数: {zone_result['total_periods']}")
        print(f"   最新模式: {zone_result['latest_pattern']}")
        
    except Exception as e:
        print(f"分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_file)
        except:
            pass


def demo_ui_integration():
    """演示UI集成功能"""
    print("\n" + "=" * 60)
    print("UI集成功能演示")
    print("=" * 60)
    
    try:
        from src.ui.app_controller import AppController
        
        print("UI集成功能包括:")
        print("1. 文件导入对话框 - 支持数据预览和验证")
        print("2. 分析选项配置对话框 - 灵活选择分析类型")
        print("3. 结果导出对话框 - 多格式导出支持")
        print("4. 进度显示和错误处理")
        print("5. 多线程处理避免界面冻结")
        
        print("\n要启动完整的GUI应用程序，请运行:")
        print("python main.py")
        
        print("\n或者运行演示脚本:")
        print("python demo_ui.py")
        
    except ImportError as e:
        print(f"UI模块导入失败: {str(e)}")
        print("请确保所有依赖包已正确安装")


def main():
    """主函数"""
    print("彩票号码分析系统 - 数据导入和分析功能集成演示")
    print("=" * 80)
    
    try:
        # 演示数据导入和验证
        demo_data_import_and_validation()
        
        # 演示分析功能集成
        demo_analysis_integration()
        
        # 演示UI集成功能
        demo_ui_integration()
        
        print("\n" + "=" * 80)
        print("演示完成！所有功能集成正常工作。")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
