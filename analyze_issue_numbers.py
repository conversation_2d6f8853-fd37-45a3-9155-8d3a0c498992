#!/usr/bin/env python3
"""
分析期号格式问题
"""

import pandas as pd
import re
from collections import Counter

def analyze_issue_numbers():
    """分析期号格式"""
    print("🔍 分析dlt_data.csv中的期号格式问题")
    print("=" * 60)
    
    # 读取数据
    df = pd.read_csv("data/dlt_data.csv")
    
    # 分析期号格式
    issue_numbers = df['期号'].astype(str).tolist()
    
    print(f"📊 期号统计:")
    print(f"总期号数量: {len(issue_numbers)}")
    
    # 按长度分组
    length_groups = {}
    for issue in issue_numbers:
        length = len(issue.strip())
        if length not in length_groups:
            length_groups[length] = []
        length_groups[length].append(issue.strip())
    
    print(f"\n📏 按长度分组:")
    for length in sorted(length_groups.keys()):
        count = len(length_groups[length])
        print(f"  {length}位数字: {count} 个")
        if count <= 10:
            print(f"    示例: {length_groups[length][:5]}")
        else:
            print(f"    示例: {length_groups[length][:5]} ...")
    
    # 检查格式模式
    print(f"\n🔍 期号格式分析:")
    
    # 4位数字 (如7001)
    pattern_4digit = r'^\d{4}$'
    matches_4 = [issue for issue in issue_numbers if re.match(pattern_4digit, issue.strip())]
    print(f"4位数字格式 (如7001): {len(matches_4)} 个")
    if matches_4:
        print(f"  范围: {min(matches_4)} - {max(matches_4)}")
    
    # 5位数字 (如24001)
    pattern_5digit = r'^\d{5}$'
    matches_5 = [issue for issue in issue_numbers if re.match(pattern_5digit, issue.strip())]
    print(f"5位数字格式 (如24001): {len(matches_5)} 个")
    if matches_5:
        print(f"  范围: {min(matches_5)} - {max(matches_5)}")
    
    # 6位数字
    pattern_6digit = r'^\d{6}$'
    matches_6 = [issue for issue in issue_numbers if re.match(pattern_6digit, issue.strip())]
    print(f"6位数字格式: {len(matches_6)} 个")
    if matches_6:
        print(f"  示例: {matches_6[:5]}")
    
    # 其他格式
    all_valid = set(matches_4 + matches_5 + matches_6)
    invalid = [issue for issue in issue_numbers if issue.strip() not in all_valid]
    print(f"其他格式: {len(invalid)} 个")
    if invalid:
        print(f"  示例: {invalid[:10]}")
    
    # 分析年份分布
    print(f"\n📅 按年份分析:")
    year_groups = {}
    
    for issue in issue_numbers:
        issue = issue.strip()
        if len(issue) == 4:
            # 4位格式，前2位是年份 (如7001 = 2007年)
            year = "20" + issue[:2]
        elif len(issue) == 5:
            # 5位格式，前2位是年份 (如24001 = 2024年)
            year = "20" + issue[:2]
        else:
            year = "其他"
        
        if year not in year_groups:
            year_groups[year] = []
        year_groups[year].append(issue)
    
    for year in sorted(year_groups.keys()):
        count = len(year_groups[year])
        print(f"  {year}: {count} 期")
        if count <= 5:
            print(f"    期号: {year_groups[year]}")
    
    # 检查当前验证器的规则
    print(f"\n⚙️  当前验证器规则:")
    print(f"期号格式要求: 5-6位数字 (正则: r'^\\d{{5,6}}$')")
    print(f"不符合规则的期号数量: {len(matches_4)} 个 (4位数字)")
    
    # 建议
    print(f"\n💡 建议:")
    if matches_4:
        print(f"1. 修改验证器规则，允许4位数字格式")
        print(f"2. 或者将4位期号转换为5位格式 (如7001 -> 07001)")
    
    print(f"3. 当前数据格式分布:")
    print(f"   - 4位格式: {len(matches_4)} 个 (2007-2009年)")
    print(f"   - 5位格式: {len(matches_5)} 个 (2010年以后)")
    
    return {
        'total': len(issue_numbers),
        '4digit': len(matches_4),
        '5digit': len(matches_5),
        '6digit': len(matches_6),
        'invalid': len(invalid)
    }

if __name__ == "__main__":
    result = analyze_issue_numbers()
    print(f"\n📈 统计结果: {result}")
