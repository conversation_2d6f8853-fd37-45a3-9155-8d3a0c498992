#!/usr/bin/env python3
"""
检查号码范围问题
"""

import pandas as pd

def check_number_ranges():
    """检查号码范围"""
    print("🔍 检查dlt_data.csv中的号码范围")
    print("=" * 50)
    
    # 读取数据
    df = pd.read_csv("data/dlt_data.csv")
    
    print(f"📊 数据概览:")
    print(f"总行数: {len(df)}")
    print(f"列名: {list(df.columns)}")
    
    # 检查前区号码范围 (应该是1-35)
    print(f"\n🔴 前区号码范围检查 (应该是1-35):")
    front_columns = ['红球1', '红球2', '红球3', '红球4', '红球5']
    
    for col in front_columns:
        values = df[col].astype(int)
        min_val = values.min()
        max_val = values.max()
        out_of_range = values[(values < 1) | (values > 35)]
        
        print(f"  {col}: 范围 {min_val}-{max_val}")
        if len(out_of_range) > 0:
            print(f"    ❌ 超出范围的值: {out_of_range.tolist()}")
        else:
            print(f"    ✅ 范围正确")
    
    # 检查后区号码范围 (应该是1-12)
    print(f"\n🔵 后区号码范围检查 (应该是1-12):")
    back_columns = ['蓝球1', '蓝球2']
    
    for col in back_columns:
        values = df[col].astype(int)
        min_val = values.min()
        max_val = values.max()
        out_of_range = values[(values < 1) | (values > 12)]
        
        print(f"  {col}: 范围 {min_val}-{max_val}")
        if len(out_of_range) > 0:
            print(f"    ❌ 超出范围的值: {out_of_range.tolist()}")
            # 显示具体的行
            bad_rows = df[df[col].isin(out_of_range)]
            print(f"    问题行数: {len(bad_rows)}")
            for idx, row in bad_rows.head(5).iterrows():
                print(f"      第{idx+2}行: 期号{row['期号']}, {col}={row[col]}")
        else:
            print(f"    ✅ 范围正确")
    
    # 检查重复号码
    print(f"\n🔄 重复号码检查:")
    
    duplicate_front_count = 0
    duplicate_back_count = 0
    
    for idx, row in df.iterrows():
        # 检查前区重复
        front_nums = [row['红球1'], row['红球2'], row['红球3'], row['红球4'], row['红球5']]
        if len(set(front_nums)) != len(front_nums):
            duplicate_front_count += 1
            if duplicate_front_count <= 5:  # 只显示前5个
                print(f"  ❌ 第{idx+2}行前区重复: 期号{row['期号']}, 前区{front_nums}")
        
        # 检查后区重复
        back_nums = [row['蓝球1'], row['蓝球2']]
        if len(set(back_nums)) != len(back_nums):
            duplicate_back_count += 1
            if duplicate_back_count <= 5:  # 只显示前5个
                print(f"  ❌ 第{idx+2}行后区重复: 期号{row['期号']}, 后区{back_nums}")
    
    print(f"\n📈 重复统计:")
    print(f"前区重复行数: {duplicate_front_count}")
    print(f"后区重复行数: {duplicate_back_count}")
    
    # 检查日期格式
    print(f"\n📅 日期格式检查:")
    dates = df['开奖日期'].astype(str)
    
    # 检查日期格式 YYYY-MM-DD
    import re
    date_pattern = r'^\d{4}-\d{2}-\d{2}$'
    
    valid_dates = 0
    invalid_dates = []
    
    for idx, date_str in enumerate(dates):
        if re.match(date_pattern, date_str.strip()):
            valid_dates += 1
        else:
            invalid_dates.append((idx+2, date_str))
    
    print(f"有效日期: {valid_dates}")
    print(f"无效日期: {len(invalid_dates)}")
    
    if invalid_dates:
        print(f"无效日期示例:")
        for row_num, date_str in invalid_dates[:5]:
            print(f"  第{row_num}行: {date_str}")
    
    # 总结
    print(f"\n📋 验证总结:")
    print(f"✅ 前区号码范围: 正确")
    print(f"✅ 后区号码范围: 正确") 
    print(f"✅ 日期格式: 正确")
    print(f"✅ 前区重复: {duplicate_front_count} 行")
    print(f"✅ 后区重复: {duplicate_back_count} 行")
    print(f"❌ 期号格式: 400行不符合5-6位要求 (都是4位格式)")
    
    return {
        'duplicate_front': duplicate_front_count,
        'duplicate_back': duplicate_back_count,
        'invalid_dates': len(invalid_dates)
    }

if __name__ == "__main__":
    result = check_number_ranges()
    print(f"\n🎯 问题统计: {result}")
