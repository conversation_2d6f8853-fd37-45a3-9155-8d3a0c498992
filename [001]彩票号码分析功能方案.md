# 彩票号码分析功能设计方案

## 1. 功能概述

本方案旨在设计一个通用的彩票号码分析系统，支持大乐透、双色球和排列五三种彩票类型的数据分析。系统将根据历史开奖数据，计算各种统计指标，包括奇偶分析、大小分析、号码遗漏分析、分区比分析等。

## 2. 彩票类型及规则

### 2.1 大乐透（已知数据）
- 前区：35个号码中选择5个（01-35）
- 后区：12个号码中选择2个（01-12）
- 开奖频率：每周一、三、六

### 2.2 双色球
- 红球区：33个号码中选择6个（01-33）
- 蓝球区：16个号码中选择1个（01-16）
- 开奖频率：每周二、四、日

### 2.3 排列五
- 5位数字，每位数字范围0-9
- 每天开奖
- 号码顺序重要

## 3. 功能需求分析

根据用户需求，需要实现以下分析功能：

### 3.1 奇偶分析
- **奇偶排布**：统计每期开奖号码中奇数和偶数的分布情况
- **奇偶码**：将每期奇偶分布转化为模式代码（如奇偶奇奇偶=10110）
- **上次出现奇偶码的日期和间隔**：记录每种奇偶模式上次出现的时间和间隔期数

### 3.2 大小分析
- **大小排布**：统计每期开奖号码中大号和小号的分布情况
- **大小码**：将每期大小分布转化为模式代码
- **上次出现大小码的日期和间隔**：记录每种大小模式上次出现的时间和间隔期数

### 3.3 号码遗漏分析
- **每个号码距离上次开出的日期和间隔**：计算每个号码的遗漏期数

### 3.4 分区比分析
- **分区比**：统计开奖号码在不同数值区间的分布比例
- **上次出现这个分区比的日期**：记录每种分区比模式上次出现的时间
- **上次出现相同0分区比日期和间隔**：记录特定分区比(0分区)上次出现的时间和间隔

## 4. 技术实现方案

### 4.1 数据结构设计

```python
class LotteryData:
    def __init__(self):
        self.issue_number = ""      # 期号
        self.draw_date = ""         # 开奖日期
        self.numbers = []           # 开奖号码列表
        self.type = ""              # 彩票类型

class LotteryAnalyzer:
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.history_data = []
```

### 4.2 核心功能模块

#### 4.2.1 奇偶分析模块
```python
def analyze_odd_even_distribution(self, numbers):
    """分析号码的奇偶分布"""
    pass

def get_odd_even_pattern(self, numbers):
    """获取奇偶模式码"""
    pass

def calculate_odd_even_intervals(self, pattern):
    """计算奇偶模式的出现间隔"""
    pass
```

#### 4.2.2 大小分析模块
```python
def analyze_big_small_distribution(self, numbers):
    """分析号码的大小分布"""
    pass

def get_big_small_pattern(self, numbers):
    """获取大小模式码"""
    pass

def calculate_big_small_intervals(self, pattern):
    """计算大小模式的出现间隔"""
    pass
```

#### 4.2.3 号码遗漏分析模块
```python
def calculate_number_intervals(self, number):
    """计算指定号码的遗漏期数"""
    pass
```

#### 4.2.4 分区比分析模块
```python
def analyze_zone_ratio(self, numbers):
    """分析分区比"""
    pass

def calculate_zone_ratio_intervals(self, ratio):
    """计算分区比模式的出现间隔"""
    pass
```

### 4.3 彩票规则定义

不同彩票类型需要定义不同的规则：

```python
LOTTERY_RULES = {
    "dlt": {
        "number_ranges": [35, 12],  // 前区35个号码，后区12个号码
        "odd_even_boundary": 2,     // 奇偶分界点（实际按数字奇偶性判断）
        "big_small_boundary": {     // 大小分界点
            "front": 18,            // 前区大于18为大号
            "back": 7               // 后区大于7为大号
        },
        "zones": [                  // 大乐透分区定义(1-35分为7个区)
            {"name": "zone1", "range": (1, 5)},
            {"name": "zone2", "range": (6, 10)},
            {"name": "zone3", "range": (11, 15)},
            {"name": "zone4", "range": (16, 20)},
            {"name": "zone5", "range": (21, 25)},
            {"name": "zone6", "range": (26, 30)},
            {"name": "zone7", "range": (31, 35)}
        ]
    },
    "ssq": {
        "number_ranges": [33, 16],
        "big_small_boundary": {
            "front": 17,
            "back": 9
        },
        "zones": [                 // 双色球分区定义
            {"name": "zone1", "range": (1, 5)},
            {"name": "zone2", "range": (6, 10)},
            {"name": "zone3", "range": (11, 15)},
            {"name": "zone4", "range": (16, 20)},
            {"name": "zone5", "range": (21, 25)},
            {"name": "zone6", "range": (26, 30)},
            {"name": "zone7", "range": (31, 33)}
        ]
    },
    "pl5": {
        "number_ranges": [10, 10, 10, 10, 10],
        "big_small_boundary": 5,
        "zones": [
            {"name": "zone1", "range": (0, 4)},
            {"name": "zone2", "range": (5, 9)}
        ]
    }
}
```

## 5. 实现步骤

### 5.1 数据处理模块
1. 读取CSV数据文件
2. 解析不同类型的彩票数据
3. 构建统一的数据结构

### 5.2 分析模块开发
1. 实现奇偶分析功能
2. 实现大小分析功能
3. 实现号码遗漏分析功能
4. 实现分区比分析功能

### 5.3 结果输出模块
1. 格式化输出分析结果
2. 提供查询接口

## 6. 预期输出

系统将输出以下分析结果：

1. 每期开奖数据的详细分析报告
2. 各种模式的历史出现记录
3. 统计数据和趋势分析
4. 可用于彩票号码生成的参考数据

## 7. 后续扩展

1. 添加图形化界面
2. 增加更多分析维度
3. 实现智能选号功能
4. 添加数据可视化功能