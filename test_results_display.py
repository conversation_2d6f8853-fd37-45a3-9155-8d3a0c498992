#!/usr/bin/env python3
"""
测试分析结果显示区域功能
"""

import sys
import os
import tkinter as tk

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_results_display_area():
    """测试分析结果显示区域"""
    try:
        from src.ui.main_window import MainWindow
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 检查分割窗口是否存在
        if hasattr(main_window, 'paned_window'):
            print("✅ 分割窗口已成功创建")
        else:
            print("❌ 分割窗口不存在")
            return False
            
        # 检查分析结果文本区域是否存在
        if hasattr(main_window, 'results_text'):
            print("✅ 分析结果文本区域已成功创建")
        else:
            print("❌ 分析结果文本区域不存在")
            return False
            
        # 检查数据表格是否存在
        if hasattr(main_window, 'data_tree'):
            print("✅ 数据表格已成功创建")
        else:
            print("❌ 数据表格不存在")
            return False
            
        # 检查文本框架是否存在
        if hasattr(main_window, 'text_frame'):
            print("✅ 文本框架已成功创建")
        else:
            print("❌ 文本框架不存在")
            return False
            
        # 检查表格框架是否存在
        if hasattr(main_window, 'table_frame'):
            print("✅ 表格框架已成功创建")
        else:
            print("❌ 表格框架不存在")
            return False
            
        # 关闭窗口
        main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_text_methods():
    """测试文本方法功能"""
    try:
        from src.ui.main_window import MainWindow
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 测试set_results_text方法
        test_text = "这是测试文本内容"
        try:
            main_window.set_results_text(test_text)
            print("✅ set_results_text方法正常工作")
        except Exception as e:
            print(f"❌ set_results_text方法失败: {e}")
            return False
            
        # 测试append_results_text方法
        append_text = "\n这是追加的文本内容"
        try:
            main_window.append_results_text(append_text)
            print("✅ append_results_text方法正常工作")
        except Exception as e:
            print(f"❌ append_results_text方法失败: {e}")
            return False
            
        # 验证文本内容
        if hasattr(main_window, 'results_text'):
            current_text = main_window.results_text.get(1.0, tk.END).strip()
            expected_text = (test_text + append_text).strip()
            if current_text == expected_text:
                print("✅ 文本内容设置和追加正确")
            else:
                print(f"⚠️ 文本内容不匹配")
                print(f"期望: {expected_text}")
                print(f"实际: {current_text}")
        
        # 关闭窗口
        main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 文本方法测试失败: {e}")
        return False

def test_layout_structure():
    """测试布局结构"""
    try:
        from src.ui.main_window import MainWindow
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 检查分割窗口的子组件数量
        if hasattr(main_window, 'paned_window'):
            # 获取分割窗口的子组件
            panes = main_window.paned_window.panes()
            if len(panes) == 2:
                print("✅ 分割窗口包含2个面板（文本区域和表格区域）")
            else:
                print(f"❌ 分割窗口面板数量不正确，期望2个，实际{len(panes)}个")
                return False
        
        # 关闭窗口
        main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 布局结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("分析结果显示区域功能测试")
    print("=" * 50)
    
    # 测试显示区域
    print("\n1. 测试分析结果显示区域...")
    display_test = test_results_display_area()
    
    # 测试文本方法
    print("\n2. 测试文本方法功能...")
    text_test = test_text_methods()
    
    # 测试布局结构
    print("\n3. 测试布局结构...")
    layout_test = test_layout_structure()
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    if display_test and text_test and layout_test:
        print("🎉 所有测试通过！分析结果显示区域功能正常")
        return True
    else:
        print("❌ 部分测试失败，需要检查实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
