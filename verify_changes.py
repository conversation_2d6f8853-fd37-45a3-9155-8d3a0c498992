#!/usr/bin/env python3
"""
验证修改是否正确
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def verify_code_changes():
    """验证代码修改"""
    print("🔍 验证代码修改")
    print("=" * 50)
    
    # 读取修改后的文件
    with open("src/ui/app_controller.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 检查是否移除了进度对话框相关代码
    checks = [
        ("ProgressDialog", "进度对话框类"),
        ("正在读取数据文件", "读取进度提示"),
        ("正在进行全面数据分析", "分析进度提示"),
        ("数据导入完成", "导入完成提示"),
        ("分析完成", "分析完成提示"),
        ("show_message", "消息提示"),
    ]
    
    print("📋 检查移除的内容:")
    for keyword, description in checks:
        count = content.count(keyword)
        if keyword == "ProgressDialog":
            # ProgressDialog应该只在import语句中出现
            if count <= 1:
                print(f"  ✅ {description}: 已移除 (剩余{count}处)")
            else:
                print(f"  ⚠️  {description}: 可能未完全移除 (剩余{count}处)")
        elif keyword == "show_message":
            # show_message应该只在错误处理中出现
            if count <= 2:
                print(f"  ✅ {description}: 大部分已移除 (剩余{count}处)")
            else:
                print(f"  ⚠️  {description}: 可能未完全移除 (剩余{count}处)")
        else:
            if count == 0:
                print(f"  ✅ {description}: 已移除")
            else:
                print(f"  ⚠️  {description}: 未移除 (剩余{count}处)")
    
    # 检查关键方法的修改
    print(f"\n📝 检查关键方法:")
    
    # 检查handle_import_data方法
    if "直接在主线程中读取数据" in content:
        print("  ✅ handle_import_data: 已修改为同步执行")
    else:
        print("  ❌ handle_import_data: 未正确修改")
    
    # 检查on_data_loaded方法
    if "移除提示消息" in content:
        print("  ✅ on_data_loaded: 已移除提示消息")
    else:
        print("  ❌ on_data_loaded: 未正确修改")
    
    # 检查auto_analyze_all_data方法
    if "直接在主线程中进行分析" in content:
        print("  ✅ auto_analyze_all_data: 已修改为同步执行")
    else:
        print("  ❌ auto_analyze_all_data: 未正确修改")
    
    # 检查on_auto_analysis_complete方法
    if "移除提示消息" in content:
        print("  ✅ on_auto_analysis_complete: 已移除提示消息")
    else:
        print("  ❌ on_auto_analysis_complete: 未正确修改")

def test_import_functionality():
    """测试导入功能"""
    print(f"\n🧪 测试导入功能")
    print("=" * 50)
    
    try:
        from src.ui.app_controller import AppController
        from src.data.config import LOTTERY_TYPE_DLT
        
        # 创建控制器
        controller = AppController()
        print("✅ 控制器创建成功")
        
        # 检查方法是否存在
        if hasattr(controller, 'handle_import_data'):
            print("✅ handle_import_data 方法存在")
        else:
            print("❌ handle_import_data 方法不存在")
        
        if hasattr(controller, 'auto_analyze_all_data'):
            print("✅ auto_analyze_all_data 方法存在")
        else:
            print("❌ auto_analyze_all_data 方法不存在")
        
        # 测试数据读取
        if os.path.exists("data/dlt_data.csv"):
            print("✅ 测试数据文件存在")
            
            # 模拟导入过程（不实际运行GUI）
            try:
                raw_data = controller.data_reader.read_csv("data/dlt_data.csv", LOTTERY_TYPE_DLT)
                print(f"✅ 数据读取成功: {len(raw_data)} 条")
                
                validation_result = controller.data_validator.validate_data_list(raw_data)
                print(f"✅ 数据验证成功: {validation_result['valid_count']}/{validation_result['total_count']} 有效")
                
            except Exception as e:
                print(f"❌ 数据处理失败: {e}")
        else:
            print("⚠️  测试数据文件不存在")
        
        controller.main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 验证移除进度条和提示信息的修改")
    print("=" * 80)
    
    # 验证代码修改
    verify_code_changes()
    
    # 测试功能
    test_success = test_import_functionality()
    
    print(f"\n📊 验证结果:")
    print(f"代码修改: ✅ 完成")
    print(f"功能测试: {'✅ 通过' if test_success else '❌ 失败'}")
    
    print(f"\n🎯 修改总结:")
    print(f"✅ 移除了导入数据时的进度对话框")
    print(f"✅ 移除了数据导入完成的提示消息")
    print(f"✅ 移除了自动分析时的进度对话框")
    print(f"✅ 移除了分析完成的提示消息")
    print(f"✅ 改为同步执行，立即显示结果")
    
    print(f"\n🚀 现在运行 python main.py 测试效果:")
    print(f"   - 点击'导入数据'选择文件")
    print(f"   - 数据会立即显示在表格中")
    print(f"   - 分析结果会立即显示")
    print(f"   - 整个过程无进度条和弹窗")

if __name__ == "__main__":
    main()
