#!/usr/bin/env python3
"""
测试表格显示功能
验证新的分页表格显示是否正常工作
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

from src.ui.main_window import MainWindow
from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_DLT

def test_table_display():
    """测试表格显示功能"""
    print("测试表格显示功能...")
    
    # 创建主窗口
    app = MainWindow()
    
    # 创建测试数据
    test_data = [
        LotteryData(
            issue_number="24001",
            draw_date=datetime(2024, 1, 1),
            numbers=[1, 5, 15, 25, 35, 2, 8],
            lottery_type=LOTTERY_TYPE_DLT
        ),
        LotteryData(
            issue_number="24002", 
            draw_date=datetime(2024, 1, 3),
            numbers=[3, 7, 17, 27, 33, 1, 9],
            lottery_type=LOTTERY_TYPE_DLT
        ),
        LotteryData(
            issue_number="24003",
            draw_date=datetime(2024, 1, 6), 
            numbers=[2, 8, 18, 28, 34, 3, 10],
            lottery_type=LOTTERY_TYPE_DLT
        )
    ]
    
    # 测试表格功能
    try:
        # 检查是否有数据表格方法
        if hasattr(app, 'set_data_table'):
            print("✅ set_data_table 方法存在")
            
            # 设置测试数据到表格
            app.set_data_table(test_data)
            print("✅ 数据已设置到表格")
            
            # 检查表格是否有数据
            if hasattr(app, 'data_tree'):
                children = app.data_tree.get_children()
                print(f"✅ 表格中有 {len(children)} 行数据")
                
                # 验证数据内容
                for i, child in enumerate(children):
                    values = app.data_tree.item(child)['values']
                    print(f"   行 {i+1}: {values}")
            else:
                print("❌ data_tree 属性不存在")
                
        else:
            print("❌ set_data_table 方法不存在")
            
        # 检查分页功能
        if hasattr(app, 'notebook'):
            print("✅ notebook 分页组件存在")
            tab_count = app.notebook.index("end")
            print(f"✅ 共有 {tab_count} 个标签页")
            
            # 获取标签页名称
            for i in range(tab_count):
                tab_text = app.notebook.tab(i, "text")
                print(f"   标签页 {i}: {tab_text}")
        else:
            print("❌ notebook 分页组件不存在")
            
        # 测试清除功能
        if hasattr(app, 'clear_data_table'):
            print("✅ clear_data_table 方法存在")
            app.clear_data_table()
            
            if hasattr(app, 'data_tree'):
                children = app.data_tree.get_children()
                print(f"✅ 清除后表格中有 {len(children)} 行数据")
        else:
            print("❌ clear_data_table 方法不存在")
            
        print("\n🎉 表格显示功能测试完成！")
        print("您可以手动测试以下功能：")
        print("1. 点击'导入数据'按钮导入CSV文件")
        print("2. 查看'数据表格'标签页中的数据显示")
        print("3. 切换到'分析结果'标签页查看分析结果")
        print("4. 点击'清除结果'按钮测试清除功能")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    # 运行主窗口（注释掉以避免阻塞）
    # app.run()

if __name__ == "__main__":
    test_table_display()
