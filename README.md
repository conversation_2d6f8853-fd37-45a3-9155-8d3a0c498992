# 彩票号码分析系统

一个专业的彩票号码分析工具，支持大乐透、双色球、排列五三种彩票类型的综合分析。

## 🎯 项目特色

- **多彩票类型支持**: 大乐透、双色球、排列五
- **多维度分析**: 奇偶分析、大小分析、号码遗漏分析、分区比分析
- **友好的图形界面**: 基于tkinter的直观操作界面
- **数据验证**: 自动检测和报告数据问题
- **结果导出**: 支持多种格式的分析结果导出
- **高性能处理**: 支持大数据量的快速分析

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 4GB+ 内存
- 100MB+ 存储空间

### 安装步骤
1. 克隆项目
   ```bash
   git clone https://github.com/your-repo/lottery-analysis.git
   cd lottery-analysis
   ```

2. 安装依赖
   ```bash
   pip install -r requirements.txt
   ```

3. 运行程序
   ```bash
   python main.py
   ```

## 📊 功能展示

### 主界面
- 彩票类型选择
- 数据导入功能
- 分析选项配置
- 结果显示和导出

### 分析功能
- **奇偶分析**: 分析号码奇偶分布规律
- **大小分析**: 分析号码大小分布规律
- **遗漏分析**: 统计各号码遗漏期数
- **分区比分析**: 分析号码分区分布比例

## 技术栈

- **开发语言**：Python 3.7+
- **核心库**：pandas、numpy
- **UI框架**：tkinter（Python内置）
- **测试框架**：pytest

## 项目结构

```
DLT选号/
├── src/                     # 源代码目录
│   ├── data/                # 数据处理模块
│   ├── analysis/            # 分析模块
│   └── ui/                  # 用户界面模块
├── tests/                   # 测试代码目录
├── docs/                    # 文档目录
├── data/                    # 数据文件目录
├── requirements.txt         # 项目依赖
└── README.md               # 项目说明
```

## 安装说明

1. 确保已安装Python 3.7或更高版本
2. 克隆项目到本地
3. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

## 🔧 使用说明

### 数据格式
支持CSV格式的彩票数据文件：

**大乐透格式**:
```csv
期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
24001,2024-01-01,1,5,15,25,35,2,8
```

**双色球格式**:
```csv
期号,开奖日期,红球1,红球2,红球3,红球4,红球5,红球6,蓝球1
24001,2024-01-02,1,5,15,25,30,33,8
```

**排列五格式**:
```csv
date,period,numbers,sum_value
2024-01-01,24001,1 2 3 4 5,15
```

### 操作流程
1. 启动程序: `python main.py`
2. 选择彩票类型
3. 导入数据文件
4. 选择分析选项
5. 开始分析
6. 查看结果
7. 导出报告

## 🧪 测试

运行所有测试：
```bash
pytest
```

运行特定测试：
```bash
pytest tests/test_analysis/
```

查看测试覆盖率：
```bash
pytest --cov=src
```

## 📈 性能指标

- **数据读取**: 1000条数据 < 10秒
- **数据验证**: 1000条数据 < 10秒
- **分析处理**: 1000条数据 < 60秒
- **内存使用**: 峰值 < 100MB

## 📚 文档

- [用户手册](docs/user_manual.md) - 详细的使用说明
- [技术文档](docs/technical_docs.md) - 系统架构和开发指南

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 邮箱: <EMAIL>
- QQ群: 123456789
- 微信群: 扫描二维码加入

---

**免责声明**: 本软件仅供学习和研究使用，不构成任何投资建议。彩票具有随机性，请理性购买。
