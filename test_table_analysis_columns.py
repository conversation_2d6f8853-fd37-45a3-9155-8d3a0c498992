#!/usr/bin/env python3
"""
测试表格分析列显示效果
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_table_analysis_columns():
    """测试表格分析列显示"""
    print("📊 测试表格分析列显示效果")
    print("=" * 60)
    
    try:
        from src.ui.main_window import MainWindow
        from src.data.models import LotteryData
        from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5
        
        # 创建主窗口
        print("🏠 创建主窗口...")
        main_window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 检查表格列
        print("\n📋 检查表格列设置:")
        if hasattr(main_window, 'data_tree'):
            columns = main_window.data_tree['columns']
            print(f"表格列: {list(columns)}")
            
            expected_columns = ['期号', '开奖日期', '前区号码', '后区号码', '奇偶模式', '大小模式', '分区分布']
            for col in expected_columns:
                if col in columns:
                    print(f"  ✅ {col}: 存在")
                else:
                    print(f"  ❌ {col}: 缺失")
            
            # 检查是否删除了类型列
            if '类型' not in columns:
                print(f"  ✅ '类型'列已删除")
            else:
                print(f"  ❌ '类型'列仍然存在")
        
        # 创建测试数据
        print("\n🧪 创建测试数据...")
        test_data = [
            # 大乐透数据
            LotteryData(
                issue_number="24001",
                draw_date=datetime(2024, 1, 1),
                numbers=[1, 5, 15, 25, 35, 2, 8],  # 前区: 奇偶偶奇奇, 大小小小大大, 分区: 1,1,1,1,1
                lottery_type=LOTTERY_TYPE_DLT
            ),
            LotteryData(
                issue_number="24002", 
                draw_date=datetime(2024, 1, 3),
                numbers=[2, 8, 18, 28, 34, 1, 9],  # 前区: 偶偶偶偶偶, 大小小小大大
                lottery_type=LOTTERY_TYPE_DLT
            ),
            # 双色球数据
            LotteryData(
                issue_number="24003",
                draw_date=datetime(2024, 1, 6), 
                numbers=[3, 7, 17, 27, 33, 5],  # 红球: 奇奇奇奇奇, 蓝球: 奇
                lottery_type=LOTTERY_TYPE_SSQ
            ),
            # 排列五数据
            LotteryData(
                issue_number="24004",
                draw_date=datetime(2024, 1, 8),
                numbers=[1, 3, 5, 7, 9],  # 全奇数, 小大大大大
                lottery_type=LOTTERY_TYPE_PL5
            )
        ]
        
        # 测试数据填充
        print("📊 测试数据填充...")
        try:
            main_window.set_data_table(test_data)
            print("✅ 数据填充成功")
            
            # 检查表格中的数据
            if hasattr(main_window, 'data_tree'):
                children = main_window.data_tree.get_children()
                print(f"✅ 表格中有 {len(children)} 行数据")
                
                # 显示前几行数据
                print(f"\n📋 表格数据预览:")
                for i, child in enumerate(children):
                    values = main_window.data_tree.item(child)['values']
                    print(f"  行{i+1}: {values}")
                    
                    # 验证分析列是否有数据
                    if len(values) >= 7:
                        issue, date, front, back, odd_even, big_small, zone = values[:7]
                        print(f"    期号: {issue}")
                        print(f"    奇偶模式: {odd_even}")
                        print(f"    大小模式: {big_small}")
                        print(f"    分区分布: {zone}")
                        print()
            
        except Exception as e:
            print(f"❌ 数据填充失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试分析方法
        print("🔧 测试分析方法...")
        try:
            for data in test_data[:2]:  # 测试前两个数据
                odd_even = main_window._calculate_odd_even_pattern(data)
                big_small = main_window._calculate_big_small_pattern(data)
                zone_dist = main_window._calculate_zone_distribution(data)
                
                print(f"期号 {data.issue_number} ({data.lottery_type}):")
                print(f"  前区号码: {data.get_front_numbers()}")
                print(f"  奇偶模式: {odd_even}")
                print(f"  大小模式: {big_small}")
                print(f"  分区分布: {zone_dist}")
                print()
                
        except Exception as e:
            print(f"❌ 分析方法测试失败: {e}")
        
        main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_application():
    """测试完整应用程序"""
    print(f"\n🚀 测试完整应用程序")
    print("=" * 60)
    
    try:
        from src.ui.app_controller import AppController
        
        print("🎮 创建应用控制器...")
        controller = AppController()
        print("✅ 应用控制器创建成功")
        
        # 检查表格列
        if hasattr(controller.main_window, 'data_tree'):
            columns = controller.main_window.data_tree['columns']
            print(f"✅ 表格列配置: {list(columns)}")
        
        controller.main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 应用程序测试失败: {e}")
        return False

def create_column_summary():
    """创建列修改总结"""
    print(f"\n📋 表格列修改总结")
    print("=" * 60)
    print(f"🗑️  删除的列:")
    print(f"  ❌ '类型' - 不再显示彩票类型")
    print(f"")
    print(f"✅ 新增的列:")
    print(f"  📊 '奇偶模式' - 显示前区号码的奇偶分布模式")
    print(f"  📊 '大小模式' - 显示前区号码的大小分布模式")
    print(f"  📊 '分区分布' - 显示前区号码在各分区的分布")
    print(f"")
    print(f"🎨 新的表格结构:")
    print(f"  1. 期号 (80px)")
    print(f"  2. 开奖日期 (100px)")
    print(f"  3. 前区号码 (150px)")
    print(f"  4. 后区号码 (80px)")
    print(f"  5. 奇偶模式 (100px) - 如: 10101")
    print(f"  6. 大小模式 (100px) - 如: 01011")
    print(f"  7. 分区分布 (120px) - 如: 1:1:1:1:1:0:0")
    print(f"")
    print(f"💡 分析信息说明:")
    print(f"  • 奇偶模式: 1=奇数, 0=偶数")
    print(f"  • 大小模式: 1=大号, 0=小号")
    print(f"  • 分区分布: 各分区的号码数量，用冒号分隔")
    print(f"    - 大乐透: 7个分区 (如: 1:1:1:1:1:0:0)")
    print(f"    - 双色球: 7个分区 (如: 1:1:1:1:1:1:0)")
    print(f"    - 排列五: 2个分区 (如: 3:2)")

def main():
    """主函数"""
    print("📊 彩票分析系统 - 表格分析列测试")
    print("=" * 80)
    
    # 测试表格列
    test1_success = test_table_analysis_columns()
    
    # 测试完整应用程序
    test2_success = test_complete_application()
    
    # 显示总结
    create_column_summary()
    
    print(f"\n📊 测试结果:")
    print(f"表格列测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"应用程序测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 表格分析列修改完成！")
        print(f"🚀 运行 python main.py 查看新的表格显示效果")
    else:
        print(f"\n❌ 仍有问题需要解决")

if __name__ == "__main__":
    main()
