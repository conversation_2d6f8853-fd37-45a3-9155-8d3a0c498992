#!/usr/bin/env python3
"""
调试数据导入功能
检查为什么点击导入数据没有反应
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_import_dialog():
    """测试导入对话框"""
    print("🔍 测试导入对话框功能")
    
    try:
        from src.ui.dialogs import FileImportDialog
        from src.data.config import LOTTERY_TYPE_DLT
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        print("✅ 创建根窗口成功")
        
        # 测试创建对话框
        print("📋 创建文件导入对话框...")
        dialog = FileImportDialog(root, LOTTERY_TYPE_DLT)
        print("✅ 文件导入对话框创建成功")
        
        # 检查对话框属性
        if hasattr(dialog, 'dialog'):
            print("✅ 对话框窗口存在")
        else:
            print("❌ 对话框窗口不存在")
        
        if hasattr(dialog, 'result'):
            print("✅ 结果属性存在")
        else:
            print("❌ 结果属性不存在")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 导入对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_events():
    """测试主窗口事件绑定"""
    print("\n🔍 测试主窗口事件绑定")
    
    try:
        from src.ui.main_window import MainWindow
        
        # 创建主窗口
        print("🏠 创建主窗口...")
        main_window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 检查事件回调
        print("🔗 检查事件回调...")
        if hasattr(main_window, 'on_import_data'):
            if main_window.on_import_data is None:
                print("⚠️  on_import_data 回调未设置")
            else:
                print("✅ on_import_data 回调已设置")
        else:
            print("❌ on_import_data 属性不存在")
        
        # 检查导入数据方法
        if hasattr(main_window, 'import_data'):
            print("✅ import_data 方法存在")
        else:
            print("❌ import_data 方法不存在")
        
        # 检查按钮绑定
        print("🔘 检查按钮绑定...")
        # 这里我们不能直接测试按钮点击，但可以检查方法是否可调用
        try:
            # 不实际调用，只检查方法是否存在
            import_method = getattr(main_window, 'import_data', None)
            if callable(import_method):
                print("✅ import_data 方法可调用")
            else:
                print("❌ import_data 方法不可调用")
        except Exception as e:
            print(f"❌ 检查方法时出错: {e}")
        
        main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 主窗口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_controller():
    """测试应用控制器"""
    print("\n🔍 测试应用控制器")
    
    try:
        from src.ui.app_controller import AppController
        
        print("🎮 创建应用控制器...")
        controller = AppController()
        print("✅ 应用控制器创建成功")
        
        # 检查事件绑定
        print("🔗 检查事件绑定...")
        if hasattr(controller.main_window, 'on_import_data'):
            if controller.main_window.on_import_data is None:
                print("❌ on_import_data 回调未绑定")
            else:
                print("✅ on_import_data 回调已绑定")
                print(f"   绑定到: {controller.main_window.on_import_data}")
        
        # 检查处理方法
        if hasattr(controller, 'handle_import_data'):
            print("✅ handle_import_data 方法存在")
        else:
            print("❌ handle_import_data 方法不存在")
        
        # 测试手动调用导入方法（不实际执行）
        print("🧪 测试方法调用...")
        try:
            # 检查方法是否可以被调用（但不实际执行）
            import_handler = getattr(controller, 'handle_import_data', None)
            if callable(import_handler):
                print("✅ handle_import_data 方法可调用")
            else:
                print("❌ handle_import_data 方法不可调用")
        except Exception as e:
            print(f"❌ 方法调用测试失败: {e}")
        
        controller.main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 应用控制器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_file_dialog():
    """测试简单的文件对话框"""
    print("\n🔍 测试简单文件对话框")
    
    try:
        import tkinter as tk
        from tkinter import filedialog
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()
        
        print("📁 测试文件选择对话框...")
        # 不实际显示对话框，只测试是否可以创建
        print("✅ 文件对话框功能正常")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 文件对话框测试失败: {e}")
        return False

def create_simple_import_test():
    """创建简单的导入测试"""
    print("\n🔧 创建简化的导入测试")
    
    try:
        from src.ui.main_window import MainWindow
        from tkinter import filedialog
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 创建简化的导入函数
        def simple_import():
            print("📁 打开文件选择对话框...")
            file_path = filedialog.askopenfilename(
                title="选择大乐透数据文件",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
                initialdir="data"
            )
            
            if file_path:
                print(f"✅ 选择的文件: {file_path}")
                main_window.data_file_path.set(file_path)
                
                # 尝试读取数据
                try:
                    from src.data.reader import DataReader
                    from src.data.config import LOTTERY_TYPE_DLT
                    
                    reader = DataReader()
                    data_list = reader.read_csv(file_path, LOTTERY_TYPE_DLT)
                    
                    print(f"✅ 成功读取 {len(data_list)} 条数据")
                    
                    # 设置到表格
                    if hasattr(main_window, 'set_data_table'):
                        main_window.set_data_table(data_list)
                        print("✅ 数据已显示在表格中")
                    
                    messagebox.showinfo("成功", f"成功导入 {len(data_list)} 条数据")
                    
                except Exception as e:
                    print(f"❌ 数据读取失败: {e}")
                    messagebox.showerror("错误", f"数据读取失败: {e}")
            else:
                print("❌ 未选择文件")
        
        # 替换导入方法
        main_window.import_data = simple_import
        main_window.on_import_data = simple_import
        
        print("✅ 简化导入功能已设置")
        print("🚀 启动主窗口...")
        print("💡 现在可以点击'导入数据'按钮测试功能")
        
        # 运行主窗口
        main_window.run()
        
    except Exception as e:
        print(f"❌ 简化导入测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🐛 调试数据导入功能")
    print("=" * 80)
    
    # 测试1: 导入对话框
    test1 = test_import_dialog()
    
    # 测试2: 主窗口事件
    test2 = test_main_window_events()
    
    # 测试3: 应用控制器
    test3 = test_app_controller()
    
    # 测试4: 简单文件对话框
    test4 = test_simple_file_dialog()
    
    print(f"\n📊 测试结果:")
    print(f"导入对话框: {'✅' if test1 else '❌'}")
    print(f"主窗口事件: {'✅' if test2 else '❌'}")
    print(f"应用控制器: {'✅' if test3 else '❌'}")
    print(f"文件对话框: {'✅' if test4 else '❌'}")
    
    if not all([test1, test2, test3, test4]):
        print(f"\n❌ 发现问题，建议使用简化版本测试")
        print(f"运行: python debug_import.py --simple")
    else:
        print(f"\n✅ 所有组件正常，问题可能在事件绑定或对话框显示")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--simple":
        create_simple_import_test()
    else:
        main()
