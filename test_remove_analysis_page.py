#!/usr/bin/env python3
"""
测试删除分析结果页面后的效果
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_analysis_page_removal():
    """测试分析结果页面删除效果"""
    print("🗑️  测试删除分析结果页面后的效果")
    print("=" * 60)
    
    try:
        from src.ui.main_window import MainWindow
        
        # 创建主窗口
        print("🏠 创建主窗口...")
        main_window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 检查是否还有notebook组件
        print("\n📋 检查界面组件:")
        if hasattr(main_window, 'notebook'):
            print("  ❌ notebook 组件仍然存在")
        else:
            print("  ✅ notebook 组件已移除")
        
        # 检查是否还有text_frame
        if hasattr(main_window, 'text_frame'):
            print("  ❌ text_frame 组件仍然存在")
        else:
            print("  ✅ text_frame 组件已移除")
        
        # 检查是否还有results_text
        if hasattr(main_window, 'results_text'):
            print("  ❌ results_text 组件仍然存在")
        else:
            print("  ✅ results_text 组件已移除")
        
        # 检查数据表格是否正常
        print("\n📊 检查数据表格:")
        if hasattr(main_window, 'data_tree'):
            print("  ✅ data_tree 组件存在")
        else:
            print("  ❌ data_tree 组件不存在")
        
        # 检查results_frame的标题
        if hasattr(main_window, 'results_frame'):
            try:
                title = main_window.results_frame.cget('text')
                print(f"  ✅ 结果区域标题: '{title}'")
                if title == "数据表格":
                    print("  ✅ 标题已更新为'数据表格'")
                else:
                    print("  ⚠️  标题未更新")
            except:
                print("  ⚠️  无法获取标题")
        
        # 检查方法兼容性
        print("\n🔧 检查方法兼容性:")
        
        # 测试set_results_text方法
        try:
            main_window.set_results_text("测试文本")
            print("  ✅ set_results_text 方法兼容")
        except Exception as e:
            print(f"  ❌ set_results_text 方法错误: {e}")
        
        # 测试append_results_text方法
        try:
            main_window.append_results_text("测试文本")
            print("  ✅ append_results_text 方法兼容")
        except Exception as e:
            print(f"  ❌ append_results_text 方法错误: {e}")
        
        # 测试clear_results方法
        try:
            main_window.clear_results()
            print("  ✅ clear_results 方法正常")
        except Exception as e:
            print(f"  ❌ clear_results 方法错误: {e}")
        
        # 测试set_data_table方法
        try:
            # 创建测试数据
            from src.data.models import LotteryData
            from src.data.config import LOTTERY_TYPE_DLT
            from datetime import datetime
            
            test_data = [
                LotteryData(
                    issue_number="24001",
                    draw_date=datetime(2024, 1, 1),
                    numbers=[1, 5, 15, 25, 35, 2, 8],
                    lottery_type=LOTTERY_TYPE_DLT
                )
            ]
            
            main_window.set_data_table(test_data)
            print("  ✅ set_data_table 方法正常")
            
            # 检查表格中是否有数据
            if hasattr(main_window, 'data_tree'):
                children = main_window.data_tree.get_children()
                print(f"  ✅ 表格中有 {len(children)} 行数据")
            
        except Exception as e:
            print(f"  ❌ set_data_table 方法错误: {e}")
        
        main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_controller_compatibility():
    """测试应用控制器兼容性"""
    print(f"\n🎮 测试应用控制器兼容性")
    print("=" * 60)
    
    try:
        from src.ui.app_controller import AppController
        
        print("🎮 创建应用控制器...")
        controller = AppController()
        print("✅ 应用控制器创建成功")
        
        # 检查主窗口是否正常
        print("🏠 检查主窗口集成...")
        if hasattr(controller.main_window, 'data_tree'):
            print("  ✅ 数据表格组件正常")
        else:
            print("  ❌ 数据表格组件缺失")
        
        # 检查事件绑定
        print("🔗 检查事件绑定...")
        if hasattr(controller.main_window, 'on_import_data'):
            if controller.main_window.on_import_data is not None:
                print("  ✅ 导入数据事件已绑定")
            else:
                print("  ❌ 导入数据事件未绑定")
        
        if hasattr(controller.main_window, 'on_clear_results'):
            if controller.main_window.on_clear_results is not None:
                print("  ✅ 清除结果事件已绑定")
            else:
                print("  ❌ 清除结果事件未绑定")
        
        controller.main_window.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 应用控制器测试失败: {e}")
        return False

def create_removal_summary():
    """创建删除总结"""
    print(f"\n📋 分析结果页面删除总结")
    print("=" * 60)
    print(f"🗑️  删除内容:")
    print(f"  ❌ 删除了Notebook分页组件")
    print(f"  ❌ 删除了'分析结果'标签页")
    print(f"  ❌ 删除了分析结果文本区域")
    print(f"  ❌ 删除了scrolledtext导入")
    print(f"")
    print(f"✅ 保留内容:")
    print(f"  ✅ 保留了数据表格功能")
    print(f"  ✅ 保留了方法兼容性")
    print(f"  ✅ 保留了清除结果功能")
    print(f"")
    print(f"🎨 新的界面结构:")
    print(f"  1. 顶部工具栏: [导入数据] [导出结果] [清除结果]")
    print(f"  2. 彩票类型选择区")
    print(f"  3. 分析选项区")
    print(f"  4. 数据表格区 (直接显示，无分页)")
    print(f"")
    print(f"💡 用户体验改进:")
    print(f"  - 界面更加简洁，专注于数据显示")
    print(f"  - 数据表格占用更多空间")
    print(f"  - 不再显示分析结果文本")
    print(f"  - 导入数据后直接显示表格")

def main():
    """主函数"""
    print("🗑️  彩票分析系统 - 删除分析结果页面测试")
    print("=" * 80)
    
    # 测试主窗口
    test1_success = test_analysis_page_removal()
    
    # 测试应用控制器
    test2_success = test_app_controller_compatibility()
    
    # 显示总结
    create_removal_summary()
    
    print(f"\n📊 测试结果:")
    print(f"主窗口测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"控制器测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 分析结果页面删除完成！")
        print(f"🚀 运行 python main.py 查看简化后的界面")
    else:
        print(f"\n❌ 仍有问题需要解决")

if __name__ == "__main__":
    main()
