# 彩票数据分析系统 - 简化版

## 概述

这是一个重新设计的简化版彩票数据分析系统，专注于核心功能：
- 使用SQLite数据库存储彩票数据
- 计算奇偶比、大小比和排布模式
- **新功能**: 计算"上次奇偶排布"和"上次分区比排布"的出现间隔
- 支持大乐透和双色球（删除了排列五支持）

## 主要特性

### ✨ 新增功能
- **上次奇偶排布**: 显示当前奇偶模式上次出现的日期和间隔期数
- **上次分区比排布**: 显示当前分区分布模式上次出现的日期和间隔期数
- **SQLite数据库**: 可靠的本地数据存储，替代文件存储
- **简化界面**: 专注于数据显示，去除复杂分析功能

### 📊 表格列说明
1. **开奖日期**: 彩票开奖日期
2. **期号**: 彩票期号
3. **开奖号码**: 完整的开奖号码（前区+后区）
4. **红球**: 前区/红球号码
5. **篮球**: 后区/蓝球号码
6. **奇偶比**: 奇数:偶数的比例
7. **奇偶排布**: 奇偶模式的二进制表示（1=奇数，0=偶数）
8. **上次奇偶排布**: 相同奇偶模式上次出现的日期和间隔，格式：2021-04-03 (5)
9. **大小比**: 大号:小号的比例
10. **大小排布**: 大小模式的二进制表示（1=大号，0=小号）
11. **上次大小排布**: 相同分区分布模式上次出现的日期和间隔

## 快速开始

### 1. 启动应用
```bash
python run_simple_app.py
```

### 2. 导入数据
1. 点击界面上的"导入数据"按钮
2. 选择CSV格式的彩票数据文件
3. 系统会自动计算所有属性并存储到数据库

### 3. 查看数据
- 使用"彩票类型"下拉框切换大乐透/双色球
- 点击"刷新数据"更新显示
- 表格会显示所有计算属性，包括新的"上次模式"列

## 数据格式要求

### 大乐透数据格式（CSV）
```csv
期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
24001,2024-01-01,1,5,12,18,25,3,8
24002,2024-01-03,2,7,11,19,24,5,10
```

### 双色球数据格式（CSV）
```csv
期号,开奖日期,红球1,红球2,红球3,红球4,红球5,红球6,蓝球1
24001,2024-01-01,1,5,12,18,25,30,8
24002,2024-01-03,2,7,11,19,24,28,10
```

## 技术说明

### 上次模式计算逻辑

#### 上次奇偶排布
1. 将当前期的奇偶排布转换为二进制字符串（如"11001"）
2. 在历史记录中查找相同的奇偶排布模式
3. 计算与上次出现的间隔期数
4. 显示格式：`2021-04-03 (5)` 表示上次在2021-04-03出现，间隔5期

#### 上次分区比排布
1. 将当前期的分区分布转换为二进制模式（有号码的分区为1，无号码为0）
2. 在历史记录中查找相同的分区分布模式
3. 计算与上次出现的间隔期数
4. 显示格式同上

### 彩票类型配置

#### 大乐透
- 前区：5个号码（1-35），大小分界点18
- 后区：2个号码（1-12），大小分界点7
- 前区分7个区，后区分2个区

#### 双色球
- 红球：6个号码（1-33），大小分界点17
- 蓝球：1个号码（1-16），大小分界点9
- 红球分7个区，蓝球分2个区

## 文件结构

```
├── src/
│   ├── database/           # 数据库模块
│   ├── calculator/         # 计算模块
│   ├── services/          # 服务模块
│   └── ui/                # 用户界面
├── project_document/      # 项目文档
├── run_simple_app.py     # 启动脚本
├── test_system.py        # 系统测试
└── lottery_data.db       # SQLite数据库文件（运行后生成）
```

## 系统测试

运行完整的系统测试：
```bash
python test_system.py
```

测试包括：
- 基础计算功能测试
- 模式计算功能测试
- 数据库操作测试
- 数据导入测试
- UI集成测试

## 注意事项

1. **数据格式**: 确保导入的CSV文件格式正确，包含所需的列
2. **数据库**: 首次运行会自动创建SQLite数据库文件
3. **性能**: 大量数据导入时可能需要一些时间进行计算
4. **备份**: 建议定期备份`lottery_data.db`数据库文件

## 更新日志

### v2.0 - 简化版重构
- ✅ 使用SQLite数据库替代文件存储
- ✅ 新增"上次奇偶排布"计算功能
- ✅ 新增"上次分区比排布"计算功能
- ✅ 简化UI界面，专注核心功能
- ✅ 删除排列五支持，只保留大乐透和双色球
- ✅ 重新设计表格列顺序
- ✅ 完整的系统测试覆盖

---

**开发完成时间**: 2025-07-28  
**版本**: 2.0 简化版
